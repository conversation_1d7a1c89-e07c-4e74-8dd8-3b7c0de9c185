{"sorted_middleware": ["/api/chat/route"], "middleware": {}, "instrumentation": null, "functions": {"/api/chat/route": {"files": ["server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/api/chat/route_client-reference-manifest.js", "server/edge/chunks/_next-internal_server_app_api_chat_route_actions_cf3bea7b.js", "server/edge/chunks/_next-internal_server_app_api_chat_route_actions_5819dece.js", "server/edge/chunks/_ba277732._.js", "server/edge/chunks/node_modules_zod_dist_esm_06684221._.js", "server/edge/chunks/node_modules_zod-to-json-schema_dist_esm_01db9d07._.js", "server/edge/chunks/node_modules_ai_dist_index_mjs_12733dd5._.js", "server/edge/chunks/node_modules_b4f607ca._.js", "server/edge/chunks/[root-of-the-server]__c69ad7fd._.js", "server/edge/chunks/edge-wrapper_08928070.js", "server/app/api/chat/route/react-loadable-manifest.js"], "name": "/api/chat", "page": "/api/chat/route", "matchers": [{"regexp": "^/api/chat(?:/)?$", "originalSource": "/api/chat"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Y4OnggEGfIu+VYChsX011xBg6Mll0vkfF3Al/Rdf5NI=", "__NEXT_PREVIEW_MODE_ID": "95a49c4846fcd7c8167862e07b93b207", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "549a40beb17909b73d254daa16ecc7009e60a8c578a8689291f5ee08c71c69c4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "df2dd45d6a9b573f49c736e0f07110eb8c9b348ce7b4305cc37e2d8f5acd0b6c"}}}}