import { openai } from "@ai-sdk/openai";
import { frontendTools } from "@assistant-ui/react-ai-sdk";
import { streamText, experimental_createMCPClient } from "ai";

export const runtime = "edge";
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages, system, tools } = await req.json();

  let mcpClient;
  let mcpTools = {};

  try {
    // Connect to the n8n MCP server via SSE
    mcpClient = await experimental_createMCPClient({
      transport: {
        type: 'sse',
        url: 'https://n8n.datosoperations.com/mcp/b2a01660-20a1-4edd-8b31-f9cdaa1810e5/sse',
      },
    });

    // Get tools from the MCP server
    mcpTools = await mcpClient.tools();
    console.log('MCP Tools available:', Object.keys(mcpTools));
  } catch (error) {
    console.error('Failed to connect to MCP server:', error);
    // Continue without MCP tools if connection fails
  }

  const result = streamText({
    model: openai("gpt-4o"),
    messages,
    // forward system prompt and tools from the frontend
    toolCallStreaming: true,
    system,
    tools: {
      ...frontendTools(tools),
      ...mcpTools, // Add MCP tools from n8n server
    },
    onError: console.log,
  });

  // Clean up MCP client after streaming is complete
  result.finishPromise.finally(() => {
    if (mcpClient) {
      mcpClient.close().catch(console.error);
    }
  });

  return result.toDataStreamResponse();
}
