(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__c69ad7fd._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/app/api/chat/route.ts [app-edge-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST),
    "maxDuration": (()=>maxDuration),
    "runtime": (()=>runtime)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$anthropic$2f$dist$2f$index$2e$mjs__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ai-sdk/anthropic/dist/index.mjs [app-edge-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$assistant$2d$ui$2f$react$2d$ai$2d$sdk$2f$dist$2f$frontendTools$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@assistant-ui/react-ai-sdk/dist/frontendTools.js [app-edge-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/ai/dist/index.mjs [app-edge-route] (ecmascript) <locals>");
;
;
;
const runtime = "edge";
const maxDuration = 30;
async function POST(req) {
    const { messages, system, tools } = await req.json();
    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
        return new Response(JSON.stringify({
            error: 'Anthropic API key not configured. Please set ANTHROPIC_API_KEY in your .env.local file.'
        }), {
            status: 400,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    let mcpClient;
    let mcpTools = {};
    try {
        // Connect to the n8n MCP server via SSE
        mcpClient = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["experimental_createMCPClient"])({
            transport: {
                type: 'sse',
                url: 'https://n8n.datosoperations.com/mcp/b2a01660-20a1-4edd-8b31-f9cdaa1810e5/sse'
            }
        });
        // Get tools from the MCP server
        mcpTools = await mcpClient.tools();
        console.log('MCP Tools available:', Object.keys(mcpTools));
    } catch (error) {
        console.error('Failed to connect to MCP server:', error);
    // Continue without MCP tools if connection fails
    }
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["streamText"])({
        model: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$anthropic$2f$dist$2f$index$2e$mjs__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["anthropic"])("claude-3-5-sonnet-20241022"),
        messages,
        // forward system prompt and tools from the frontend
        toolCallStreaming: true,
        system,
        tools: {
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$assistant$2d$ui$2f$react$2d$ai$2d$sdk$2f$dist$2f$frontendTools$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["frontendTools"])(tools),
            ...mcpTools
        },
        onError: console.log
    });
    // Clean up MCP client after streaming is complete
    result.finishPromise.finally(()=>{
        if (mcpClient) {
            mcpClient.close().catch(console.error);
        }
    });
    return result.toDataStreamResponse();
}
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__c69ad7fd._.js.map