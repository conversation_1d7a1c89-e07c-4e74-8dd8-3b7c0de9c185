{"name": "assistant-ui-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.22", "@assistant-ui/react": "^0.10.9", "@assistant-ui/react-ai-sdk": "^0.10.9", "@assistant-ui/react-markdown": "^0.10.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}