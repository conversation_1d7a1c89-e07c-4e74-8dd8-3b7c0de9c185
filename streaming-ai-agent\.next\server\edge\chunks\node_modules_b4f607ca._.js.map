{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/ai-sdk-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/api-call-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/empty-response-body-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/get-error-message.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/invalid-argument-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/invalid-prompt-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/invalid-response-data-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/json-parse-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/load-api-key-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/load-setting-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/no-content-generated-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/no-such-model-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/too-many-embedding-values-for-call-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/type-validation-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/unsupported-functionality-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/json-value/is-json.ts"], "sourcesContent": ["/**\n * Symbol used for identifying AI SDK Error instances.\n * Enables checking if an error is an instance of AISDKError across package versions.\n */\nconst marker = 'vercel.ai.error';\nconst symbol = Symbol.for(marker);\n\n/**\n * Custom error class for AI SDK related errors.\n * @extends Error\n */\nexport class AISDKError extends Error {\n  private readonly [symbol] = true; // used in isInstance\n\n  /**\n   * The underlying cause of the error, if any.\n   */\n  readonly cause?: unknown;\n\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name,\n    message,\n    cause,\n  }: {\n    name: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = name;\n    this.cause = cause;\n  }\n\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error: unknown): error is AISDKError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  protected static hasMarker(error: unknown, marker: string): boolean {\n    const markerSymbol = Symbol.for(marker);\n    return (\n      error != null &&\n      typeof error === 'object' &&\n      markerSymbol in error &&\n      typeof error[markerSymbol] === 'boolean' &&\n      error[markerSymbol] === true\n    );\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_APICallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class APICallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is APICallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_EmptyResponseBodyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class EmptyResponseBodyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is EmptyResponseBodyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidArgumentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A function argument is invalid.\n */\nexport class InvalidArgumentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly argument: string;\n\n  constructor({\n    message,\n    cause,\n    argument,\n  }: {\n    argument: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.argument = argument;\n  }\n\n  static isInstance(error: unknown): error is InvalidArgumentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidPromptError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A prompt is invalid. This error should be thrown by providers when they cannot\n * process a prompt.\n */\nexport class InvalidPromptError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly prompt: unknown;\n\n  constructor({\n    prompt,\n    message,\n    cause,\n  }: {\n    prompt: unknown;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message: `Invalid prompt: ${message}`, cause });\n\n    this.prompt = prompt;\n  }\n\n  static isInstance(error: unknown): error is InvalidPromptError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidResponseDataError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Server returned a response with invalid data content.\n * This should be thrown by providers when they cannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is InvalidResponseDataError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_JSONParseError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n// TODO v5: rename to ParseError\nexport class JSONParseError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly text: string;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super({\n      name,\n      message:\n        `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.text = text;\n  }\n\n  static isInstance(error: unknown): error is JSONParseError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadAPIKeyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadAPIKeyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadAPIKeyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadSettingError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadSettingError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadSettingError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoContentGeneratedError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is NoContentGeneratedError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoSuchModelError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class NoSuchModelError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly modelId: string;\n  readonly modelType: 'languageModel' | 'textEmbeddingModel' | 'imageModel';\n\n  constructor({\n    errorName = name,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`,\n  }: {\n    errorName?: string;\n    modelId: string;\n    modelType: 'languageModel' | 'textEmbeddingModel' | 'imageModel';\n    message?: string;\n  }) {\n    super({ name: errorName, message });\n\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n\n  static isInstance(error: unknown): error is NoSuchModelError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_TooManyEmbeddingValuesForCallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TooManyEmbeddingValuesForCallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super({\n      name,\n      message:\n        `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    });\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInstance(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_TypeValidationError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TypeValidationError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly value: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super({\n      name,\n      message:\n        `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is TypeValidationError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause,\n  }: {\n    value: unknown;\n    cause: unknown;\n  }): TypeValidationError {\n    return TypeValidationError.isInstance(cause) && cause.value === value\n      ? cause\n      : new TypeValidationError({ value, cause });\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_UnsupportedFunctionalityError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class UnsupportedFunctionalityError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly functionality: string;\n\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`,\n  }: {\n    functionality: string;\n    message?: string;\n  }) {\n    super({ name, message });\n    this.functionality = functionality;\n  }\n\n  static isInstance(error: unknown): error is UnsupportedFunctionalityError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { JSONArray, JSONObject, JSONValue } from './json-value';\n\nexport function isJSONValue(value: unknown): value is JSONValue {\n  if (\n    value === null ||\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean'\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n\n  if (typeof value === 'object') {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    );\n  }\n\n  return false;\n}\n\nexport function isJSONArray(value: unknown): value is JSONArray {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\n\nexport function isJSONObject(value: unknown): value is JSONObject {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    )\n  );\n}\n"], "names": ["name", "marker", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,SAAS;AACf,IAAM,SAAS,OAAO,GAAA,CAAI,MAAM;AALhC,IAAA;AAWO,IAAM,cAAN,MAAM,oBAAmB,MAAM;IAAA;;;;;;;GAAA,GAgBpC,YAAY,EACV,MAAAA,MAAAA,EACA,OAAA,EACA,KAAA,EACF,CAIG;QACD,KAAA,CAAM,OAAO;QAxBf,IAAA,CAAkB,GAAA,GAAU;QA0B1B,IAAA,CAAK,IAAA,GAAOA;QACZ,IAAA,CAAK,KAAA,GAAQ;IACf;IAAA;;;;GAAA,GAOA,OAAO,WAAW,KAAA,EAAqC;QACrD,OAAO,YAAW,SAAA,CAAU,OAAO,MAAM;IAC3C;IAEA,OAAiB,UAAU,KAAA,EAAgBC,QAAAA,EAAyB;QAClE,MAAM,eAAe,OAAO,GAAA,CAAIA,QAAM;QACtC,OACE,SAAS,QACT,OAAO,UAAU,YACjB,gBAAgB,SAChB,OAAO,KAAA,CAAM,YAAY,CAAA,KAAM,aAC/B,KAAA,CAAM,YAAY,CAAA,KAAM;IAE5B;AACF;AAjDoB,KAAA;AADb,IAAM,aAAN;;ACTP,IAAM,OAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmB,IAAI,EAAA;AACtC,IAAMC,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,eAAN,cAA2B,WAAW;IAa3C,YAAY,EACV,OAAA,EACA,GAAA,EACA,iBAAA,EACA,UAAA,EACA,eAAA,EACA,YAAA,EACA,KAAA,EACA,cAAc,cAAc,QAAA,CACzB,eAAe,OAAA,kBAAA;IACd,eAAe,OAAA,WAAA;IACf,eAAe,OAAA,oBAAA;IACf,cAAc,GAAA,CAAA,EAAA,eAAA;IAClB,IAAA,EACF,CAUG;QACD,KAAA,CAAM;YAAE;YAAM;YAAS;QAAM,CAAC;QArChC,IAAA,CAAkBA,IAAAA,GAAU;QAuC1B,IAAA,CAAK,GAAA,GAAM;QACX,IAAA,CAAK,iBAAA,GAAoB;QACzB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAuC;QACvD,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAnDoBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,yBAAN,cAAqC,WAAW;IAAA,qBAAA;IAGrD,YAAY,EAAE,UAAU,qBAAA,CAAsB,CAAA,GAA0B,CAAC,CAAA,CAAG;QAC1E,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAAiD;QACjE,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACPb,SAAS,gBAAgB,KAAA,EAA4B;IAC1D,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAA;IACf;IAEA,OAAO,KAAK,SAAA,CAAU,KAAK;AAC7B;;ACZA,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AASO,IAAM,uBAAN,cAAmC,WAAW;IAKnD,YAAY,EACV,OAAA,EACA,KAAA,EACA,QAAA,EACF,CAIG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;YAAS;QAAM,CAAC;QAbhC,IAAA,CAAkBG,IAAAA,GAAU;QAe1B,IAAA,CAAK,QAAA,GAAW;IAClB;IAEA,OAAO,WAAW,KAAA,EAA+C;QAC/D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AArBoBE,MAAAD;;ACRpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,qBAAN,cAAiC,WAAW;IAKjD,YAAY,EACV,MAAA,EACA,OAAA,EACA,KAAA,EACF,CAIG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM,SAAS,CAAA,gBAAA,EAAmB,OAAO,EAAA;YAAI;QAAM,CAAC;QAb9D,IAAA,CAAkBG,IAAAA,GAAU;QAe1B,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,OAAO,WAAW,KAAA,EAA6C;QAC7D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AArBoBE,MAAAD;;ACTpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,2BAAN,cAAuC,WAAW;IAKvD,YAAY,EACV,IAAA,EACA,UAAU,CAAA,uBAAA,EAA0B,KAAK,SAAA,CAAU,IAAI,CAAC,CAAA,CAAA,CAAA,EAC1D,CAGG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAXzB,IAAA,CAAkBG,IAAAA,GAAU;QAa1B,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAmD;QACnE,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAnBoBE,MAAAD;;ACRpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AALhC,IAAAE;AAQO,IAAM,iBAAN,cAA6B,WAAW;IAK7C,YAAY,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,CAAqC;QAC7D,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,2BAAA,EACS,IAAI,CAAA;eAAA,EACK,gBAAgB,KAAK,CAAC,EAAA;YAC1C;QACF,CAAC;QAZH,IAAA,CAAkBG,IAAAA,GAAU;QAc1B,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAyC;QACzD,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AApBoBE,MAAAD;;ACPpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,kBAAN,cAA8B,WAAW;IAAA,qBAAA;IAG9C,YAAY,EAAE,OAAA,CAAQ,CAAA,CAAwB;QAC5C,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAA0C;QAC1D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,mBAAN,cAA+B,WAAW;IAAA,qBAAA;IAG/C,YAAY,EAAE,OAAA,CAAQ,CAAA,CAAwB;QAC5C,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAA2C;QAC3D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AASO,IAAM,0BAAN,cAAsC,WAAW;IAAA,qBAAA;IAGtD,YAAY,EACV,UAAU,uBAAA,EACZ,GAA0B,CAAC,CAAA,CAAG;QAC5B,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QALzB,IAAA,CAAkBG,KAAAA,GAAU;IAM5B;IAEA,OAAO,WAAW,KAAA,EAAkD;QAClE,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAXoBE,OAAAD;;ACRpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,mBAAN,cAA+B,WAAW;IAM/C,YAAY,EACV,YAAYH,MAAAA,EACZ,OAAA,EACA,SAAA,EACA,UAAU,CAAA,QAAA,EAAW,SAAS,CAAA,EAAA,EAAK,OAAO,EAAA,EAC5C,CAKG;QACD,KAAA,CAAM;YAAE,MAAM;YAAW;QAAQ,CAAC;QAhBpC,IAAA,CAAkBG,KAAAA,GAAU;QAkB1B,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,SAAA,GAAY;IACnB;IAEA,OAAO,WAAW,KAAA,EAA2C;QAC3D,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAzBoBE,OAAAD;;ACLpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,qCAAN,cAAiD,WAAW;IAQjE,YAAY,OAAA,CAKT;QACD,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,iDAAA,EACO,QAAQ,QAAQ,CAAA,QAAA,EAAW,QAAQ,OAAO,CAAA,uBAAA,EAC9C,QAAQ,oBAAoB,CAAA,sBAAA,EAAyB,QAAQ,MAAA,CAAO,MAAM,CAAA,sBAAA,CAAA;QACjF,CAAC;QAnBH,IAAA,CAAkBG,KAAAA,GAAU;QAqB1B,IAAA,CAAK,QAAA,GAAW,QAAQ,QAAA;QACxB,IAAA,CAAK,OAAA,GAAU,QAAQ,OAAA;QACvB,IAAA,CAAK,oBAAA,GAAuB,QAAQ,oBAAA;QACpC,IAAA,CAAK,MAAA,GAAS,QAAQ,MAAA;IACxB;IAEA,OAAO,WACL,KAAA,EAC6C;QAC7C,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAhCoBE,OAAAD;;ACJpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AALhC,IAAAE;AAOO,IAAM,uBAAN,MAAM,6BAA4B,WAAW;IAKlD,YAAY,EAAE,KAAA,EAAO,KAAA,CAAM,CAAA,CAAuC;QAChE,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,+BAAA,EACU,KAAK,SAAA,CAAU,KAAK,CAAC,CAAA;eAAA,EACb,gBAAgB,KAAK,CAAC,EAAA;YAC1C;QACF,CAAC;QAZH,IAAA,CAAkBG,KAAAA,GAAU;QAc1B,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,OAAO,WAAW,KAAA,EAA8C;QAC9D,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;IAAA;;;;;;;;;GAAA,GAYA,OAAO,KAAK,EACV,KAAA,EACA,KAAA,EACF,EAGwB;QACtB,OAAO,qBAAoB,UAAA,CAAW,KAAK,KAAK,MAAM,KAAA,KAAU,QAC5D,QACA,IAAI,qBAAoB;YAAE;YAAO;QAAM,CAAC;IAC9C;AACF;AA1CoBE,OAAAD;AADb,IAAM,sBAAN;;ACLP,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,gCAAN,cAA4C,WAAW;IAK5D,YAAY,EACV,aAAA,EACA,UAAU,CAAA,CAAA,EAAI,aAAa,CAAA,8BAAA,CAAA,EAC7B,CAGG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAXzB,IAAA,CAAkBG,KAAAA,GAAU;QAY1B,IAAA,CAAK,aAAA,GAAgB;IACvB;IAEA,OAAO,WAAW,KAAA,EAAwD;QACxE,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAlBoBE,OAAAD;;ACLb,SAAS,YAAY,KAAA,EAAoC;IAC9D,IACE,UAAU,QACV,OAAO,UAAU,YACjB,OAAO,UAAU,YACjB,OAAO,UAAU,WACjB;QACA,OAAO;IACT;IAEA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACxB,OAAO,MAAM,KAAA,CAAM,WAAW;IAChC;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,OAAO,OAAA,CAAQ,KAAK,EAAE,KAAA,CAC3B,CAAC,CAAC,KAAK,GAAG,CAAA,GAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;IAE9D;IAEA,OAAO;AACT;AAEO,SAAS,YAAY,KAAA,EAAoC;IAC9D,OAAO,MAAM,OAAA,CAAQ,KAAK,KAAK,MAAM,KAAA,CAAM,WAAW;AACxD;AAEO,SAAS,aAAa,KAAA,EAAqC;IAChE,OACE,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,OAAA,CAAQ,KAAK,EAAE,KAAA,CACpB,CAAC,CAAC,KAAK,GAAG,CAAA,GAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;AAGhE", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/nanoid/non-secure/index.js"], "sourcesContent": ["let urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\nlet customAlphabet = (alphabet, defaultSize = 21) => {\n  return (size = defaultSize) => {\n    let id = ''\n    let i = size | 0\n    while (i--) {\n      id += alphabet[(Math.random() * alphabet.length) | 0]\n    }\n    return id\n  }\n}\nlet nanoid = (size = 21) => {\n  let id = ''\n  let i = size | 0\n  while (i--) {\n    id += urlAlphabet[(Math.random() * 64) | 0]\n  }\n  return id\n}\nexport { nanoid, customAlphabet }\n"], "names": [], "mappings": ";;;;AAAA,IAAI,cACF;AACF,IAAI,iBAAiB,CAAC,UAAU,cAAc,EAAE;IAC9C,OAAO,CAAC,OAAO,WAAW;QACxB,IAAI,KAAK;QACT,IAAI,IAAI,OAAO;QACf,MAAO,IAAK;YACV,MAAM,QAAQ,CAAC,AAAC,KAAK,MAAM,KAAK,SAAS,MAAM,GAAI,EAAE;QACvD;QACA,OAAO;IACT;AACF;AACA,IAAI,SAAS,CAAC,OAAO,EAAE;IACrB,IAAI,KAAK;IACT,IAAI,IAAI,OAAO;IACf,MAAO,IAAK;QACV,MAAM,WAAW,CAAC,AAAC,KAAK,MAAM,KAAK,KAAM,EAAE;IAC7C;IACA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/secure-json-parse/index.js"], "sourcesContent": ["'use strict'\n\nconst hasBuffer = typeof Buffer !== 'undefined'\nconst suspectProtoRx = /\"(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])(?:p|\\\\u0070)(?:r|\\\\u0072)(?:o|\\\\u006[Ff])(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])\"\\s*:/\nconst suspectConstructorRx = /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/\n\nfunction _parse (text, reviver, options) {\n  // Normalize arguments\n  if (options == null) {\n    if (reviver !== null && typeof reviver === 'object') {\n      options = reviver\n      reviver = undefined\n    }\n  }\n\n  if (hasBuffer && Buffer.isBuffer(text)) {\n    text = text.toString()\n  }\n\n  // BOM checker\n  if (text && text.charCodeAt(0) === 0xFEFF) {\n    text = text.slice(1)\n  }\n\n  // Parse normally, allowing exceptions\n  const obj = JSON.parse(text, reviver)\n\n  // Ignore null and non-objects\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n\n  const protoAction = (options && options.protoAction) || 'error'\n  const constructorAction = (options && options.constructorAction) || 'error'\n\n  // options: 'error' (default) / 'remove' / 'ignore'\n  if (protoAction === 'ignore' && constructorAction === 'ignore') {\n    return obj\n  }\n\n  if (protoAction !== 'ignore' && constructorAction !== 'ignore') {\n    if (suspectProtoRx.test(text) === false && suspectConstructorRx.test(text) === false) {\n      return obj\n    }\n  } else if (protoAction !== 'ignore' && constructorAction === 'ignore') {\n    if (suspectProtoRx.test(text) === false) {\n      return obj\n    }\n  } else {\n    if (suspectConstructorRx.test(text) === false) {\n      return obj\n    }\n  }\n\n  // Scan result for proto keys\n  return filter(obj, { protoAction, constructorAction, safe: options && options.safe })\n}\n\nfunction filter (obj, { protoAction = 'error', constructorAction = 'error', safe } = {}) {\n  let next = [obj]\n\n  while (next.length) {\n    const nodes = next\n    next = []\n\n    for (const node of nodes) {\n      if (protoAction !== 'ignore' && Object.prototype.hasOwnProperty.call(node, '__proto__')) { // Avoid calling node.hasOwnProperty directly\n        if (safe === true) {\n          return null\n        } else if (protoAction === 'error') {\n          throw new SyntaxError('Object contains forbidden prototype property')\n        }\n\n        delete node.__proto__ // eslint-disable-line no-proto\n      }\n\n      if (constructorAction !== 'ignore' &&\n          Object.prototype.hasOwnProperty.call(node, 'constructor') &&\n          Object.prototype.hasOwnProperty.call(node.constructor, 'prototype')) { // Avoid calling node.hasOwnProperty directly\n        if (safe === true) {\n          return null\n        } else if (constructorAction === 'error') {\n          throw new SyntaxError('Object contains forbidden prototype property')\n        }\n\n        delete node.constructor\n      }\n\n      for (const key in node) {\n        const value = node[key]\n        if (value && typeof value === 'object') {\n          next.push(value)\n        }\n      }\n    }\n  }\n  return obj\n}\n\nfunction parse (text, reviver, options) {\n  const stackTraceLimit = Error.stackTraceLimit\n  Error.stackTraceLimit = 0\n  try {\n    return _parse(text, reviver, options)\n  } finally {\n    Error.stackTraceLimit = stackTraceLimit\n  }\n}\n\nfunction safeParse (text, reviver) {\n  const stackTraceLimit = Error.stackTraceLimit\n  Error.stackTraceLimit = 0\n  try {\n    return _parse(text, reviver, { safe: true })\n  } catch (_e) {\n    return null\n  } finally {\n    Error.stackTraceLimit = stackTraceLimit\n  }\n}\n\nmodule.exports = parse\nmodule.exports.default = parse\nmodule.exports.parse = parse\nmodule.exports.safeParse = safeParse\nmodule.exports.scan = filter\n"], "names": [], "mappings": "AAEyB;AAFzB;AAEA,MAAM,YAAY,OAAO,qHAAA,CAAA,SAAM,KAAK;AACpC,MAAM,iBAAiB;AACvB,MAAM,uBAAuB;AAE7B,SAAS,OAAQ,IAAI,EAAE,OAAO,EAAE,OAAO;IACrC,sBAAsB;IACtB,IAAI,WAAW,MAAM;QACnB,IAAI,YAAY,QAAQ,OAAO,YAAY,UAAU;YACnD,UAAU;YACV,UAAU;QACZ;IACF;IAEA,IAAI,aAAa,qHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,OAAO;QACtC,OAAO,KAAK,QAAQ;IACtB;IAEA,cAAc;IACd,IAAI,QAAQ,KAAK,UAAU,CAAC,OAAO,QAAQ;QACzC,OAAO,KAAK,KAAK,CAAC;IACpB;IAEA,sCAAsC;IACtC,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM;IAE7B,8BAA8B;IAC9B,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;QAC3C,OAAO;IACT;IAEA,MAAM,cAAc,AAAC,WAAW,QAAQ,WAAW,IAAK;IACxD,MAAM,oBAAoB,AAAC,WAAW,QAAQ,iBAAiB,IAAK;IAEpE,mDAAmD;IACnD,IAAI,gBAAgB,YAAY,sBAAsB,UAAU;QAC9D,OAAO;IACT;IAEA,IAAI,gBAAgB,YAAY,sBAAsB,UAAU;QAC9D,IAAI,eAAe,IAAI,CAAC,UAAU,SAAS,qBAAqB,IAAI,CAAC,UAAU,OAAO;YACpF,OAAO;QACT;IACF,OAAO,IAAI,gBAAgB,YAAY,sBAAsB,UAAU;QACrE,IAAI,eAAe,IAAI,CAAC,UAAU,OAAO;YACvC,OAAO;QACT;IACF,OAAO;QACL,IAAI,qBAAqB,IAAI,CAAC,UAAU,OAAO;YAC7C,OAAO;QACT;IACF;IAEA,6BAA6B;IAC7B,OAAO,OAAO,KAAK;QAAE;QAAa;QAAmB,MAAM,WAAW,QAAQ,IAAI;IAAC;AACrF;AAEA,SAAS,OAAQ,GAAG,EAAE,EAAE,cAAc,OAAO,EAAE,oBAAoB,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACrF,IAAI,OAAO;QAAC;KAAI;IAEhB,MAAO,KAAK,MAAM,CAAE;QAClB,MAAM,QAAQ;QACd,OAAO,EAAE;QAET,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,gBAAgB,YAAY,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,cAAc;gBACvF,IAAI,SAAS,MAAM;oBACjB,OAAO;gBACT,OAAO,IAAI,gBAAgB,SAAS;oBAClC,MAAM,IAAI,YAAY;gBACxB;gBAEA,OAAO,KAAK,SAAS,CAAC,+BAA+B;;YACvD;YAEA,IAAI,sBAAsB,YACtB,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,kBAC3C,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE,cAAc;gBACvE,IAAI,SAAS,MAAM;oBACjB,OAAO;gBACT,OAAO,IAAI,sBAAsB,SAAS;oBACxC,MAAM,IAAI,YAAY;gBACxB;gBAEA,OAAO,KAAK,WAAW;YACzB;YAEA,IAAK,MAAM,OAAO,KAAM;gBACtB,MAAM,QAAQ,IAAI,CAAC,IAAI;gBACvB,IAAI,SAAS,OAAO,UAAU,UAAU;oBACtC,KAAK,IAAI,CAAC;gBACZ;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,MAAO,IAAI,EAAE,OAAO,EAAE,OAAO;IACpC,MAAM,kBAAkB,MAAM,eAAe;IAC7C,MAAM,eAAe,GAAG;IACxB,IAAI;QACF,OAAO,OAAO,MAAM,SAAS;IAC/B,SAAU;QACR,MAAM,eAAe,GAAG;IAC1B;AACF;AAEA,SAAS,UAAW,IAAI,EAAE,OAAO;IAC/B,MAAM,kBAAkB,MAAM,eAAe;IAC7C,MAAM,eAAe,GAAG;IACxB,IAAI;QACF,OAAO,OAAO,MAAM,SAAS;YAAE,MAAM;QAAK;IAC5C,EAAE,OAAO,IAAI;QACX,OAAO;IACT,SAAU;QACR,MAAM,eAAe,GAAG;IAC1B;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,OAAO,GAAG;AACzB,OAAO,OAAO,CAAC,KAAK,GAAG;AACvB,OAAO,OAAO,CAAC,SAAS,GAAG;AAC3B,OAAO,OAAO,CAAC,IAAI,GAAG", "ignoreList": [0]}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/combine-headers.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/convert-async-iterator-to-readable-stream.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/delay.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/event-source-parser-stream.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/extract-response-headers.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/generate-id.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/get-error-message.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/get-from-api.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/remove-undefined-entries.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/is-abort-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/load-api-key.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/load-optional-setting.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/load-setting.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/parse-json.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/validate-types.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/validator.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/parse-provider-options.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/post-to-api.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/resolve.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/response-handler.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/uint8-utils.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/without-trailing-slash.ts"], "sourcesContent": ["export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncIterator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncIterator.\n * @param { <T>} iterator - The AsyncIterator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncIterator.\n */\nexport function convertAsyncIteratorToReadableStream<T>(\n  iterator: AsyncIterator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "/**\n * Creates a Promise that resolves after a specified delay\n * @param delayInMs - The delay duration in milliseconds. If null or undefined, resolves immediately.\n * @returns A Promise that resolves after the specified delay\n */\nexport async function delay(delayInMs?: number | null): Promise<void> {\n  return delayInMs == null\n    ? Promise.resolve()\n    : new Promise(resolve => setTimeout(resolve, delayInMs));\n}\n", "export type EventSourceChunk = {\n  event: string | undefined;\n  data: string;\n  id?: string;\n  retry?: number;\n};\n\nexport function createEventSourceParserStream() {\n  let buffer = '';\n  let event: string | undefined = undefined;\n  let data: string[] = [];\n  let lastEventId: string | undefined = undefined;\n  let retry: number | undefined = undefined;\n\n  function parseLine(\n    line: string,\n    controller: TransformStreamDefaultController<EventSourceChunk>,\n  ) {\n    // Empty line means dispatch the event\n    if (line === '') {\n      dispatchEvent(controller);\n      return;\n    }\n\n    // Comments start with colon\n    if (line.startsWith(':')) {\n      return;\n    }\n\n    // Field parsing\n    const colonIndex = line.indexOf(':');\n    if (colonIndex === -1) {\n      // field with no value\n      handleField(line, '');\n      return;\n    }\n\n    const field = line.slice(0, colonIndex);\n    // If there's a space after the colon, it should be ignored\n    const valueStart = colonIndex + 1;\n    const value =\n      valueStart < line.length && line[valueStart] === ' '\n        ? line.slice(valueStart + 1)\n        : line.slice(valueStart);\n\n    handleField(field, value);\n  }\n\n  function dispatchEvent(\n    controller: TransformStreamDefaultController<EventSourceChunk>,\n  ) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join('\\n'),\n        id: lastEventId,\n        retry,\n      });\n\n      // Reset data but keep lastEventId as per spec\n      data = [];\n      event = undefined;\n      retry = undefined;\n    }\n  }\n\n  function handleField(field: string, value: string) {\n    switch (field) {\n      case 'event':\n        event = value;\n        break;\n      case 'data':\n        data.push(value);\n        break;\n      case 'id':\n        lastEventId = value;\n        break;\n      case 'retry':\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n\n  return new TransformStream<string, EventSourceChunk>({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n\n      buffer = incompleteLine;\n\n      // using for loop for performance\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    },\n  });\n}\n\n// performance: send in already scanned buffer separately, do not scan again\nfunction splitLines(buffer: string, chunk: string) {\n  const lines: Array<string> = [];\n  let currentLine = buffer;\n\n  // using for loop for performance\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n\n    // order is performance-optimized\n    if (char === '\\n') {\n      // Standalone LF\n      lines.push(currentLine);\n      currentLine = '';\n    } else if (char === '\\r') {\n      lines.push(currentLine);\n      currentLine = '';\n      if (chunk[i] === '\\n') {\n        i++; // CRLF case: Skip the LF character\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n\n  return { lines, incompleteLine: currentLine };\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(\n  response: Response,\n): Record<string, string> {\n  const headers: Record<string, string> = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { customAlphabet } from 'nanoid/non-secure';\n\n/**\nCreates an ID generator.\nThe total length of the ID is the sum of the prefix, separator, and random part length.\nNon-secure.\n\n@param alphabet - The alphabet to use for the ID. Default: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.\n@param prefix - The prefix of the ID to generate. Default: ''.\n@param separator - The separator between the prefix and the random part of the ID. Default: '-'.\n@param size - The size of the random part of the ID to generate. Default: 16.\n */\n// TODO 5.0 breaking change: change the return type to IDGenerator\nexport const createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  separator = '-',\n}: {\n  prefix?: string;\n  separator?: string;\n  size?: number;\n  alphabet?: string;\n} = {}): ((size?: number) => string) => {\n  const generator = customAlphabet(alphabet, defaultSize);\n\n  if (prefix == null) {\n    return generator;\n  }\n\n  // check that the prefix is not part of the alphabet (otherwise prefix checking can fail randomly)\n  if (alphabet.includes(separator)) {\n    throw new InvalidArgumentError({\n      argument: 'separator',\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`,\n    });\n  }\n\n  return size => `${prefix}${separator}${generator(size)}`;\n};\n\n/**\nA function that generates an ID.\n */\nexport type IDGenerator = () => string;\n\n/**\nGenerates a 16-character random string to use for IDs. Not secure.\n\n@param size - The size of the ID to generate. Default: 16.\n */\nexport const generateId = createIdGenerator();\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { FetchFunction } from './fetch-function';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\nimport { isAbortError } from './is-abort-error';\nimport { extractResponseHeaders } from './extract-response-headers';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const getFromApi = async <T>({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {},\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {},\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {},\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {},\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n      if (cause != null) {\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {},\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "/**\n * Removes entries from a record where the value is null or undefined.\n * @param record - The input object whose entries may be null or undefined.\n * @returns A new object containing only entries with non-null and non-undefined values.\n */\nexport function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    error instanceof Error &&\n    (error.name === 'AbortError' || error.name === 'TimeoutError')\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "/**\n * Loads an optional `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @returns The setting value.\n */\nexport function loadOptionalSetting({\n  settingValue,\n  environmentVariableName,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n}): string | undefined {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null || typeof process === 'undefined') {\n    return undefined;\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null || typeof settingValue !== 'string') {\n    return undefined;\n  }\n\n  return settingValue;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\n/**\n * Loads a `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @param settingName - The setting name.\n * @param description - The description of the setting.\n * @returns The setting value.\n */\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter. ` +\n        `Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter ` +\n        `or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting must be a string. ` +\n        `The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "import {\n  JSONParseError,\n  <PERSON>SO<PERSON>V<PERSON><PERSON>,\n  TypeValidationError,\n} from '@ai-sdk/provider';\nimport SecureJSON from 'secure-json-parse';\nimport { ZodSchema } from 'zod';\nimport { safeValidateTypes, validateTypes } from './validate-types';\nimport { Validator } from './validator';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {JSONValue} - The parsed JSON object.\n */\nexport function parseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): JSONValue;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns {T} - The parsed object.\n */\nexport function parseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): T;\nexport function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): T {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isInstance(error) ||\n      TypeValidationError.isInstance(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T; rawValue: unknown }\n  | { success: false; error: JSONParseError | TypeValidationError };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {object} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport function safeParseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): ParseResult<JSONValue>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeParseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): ParseResult<T>;\nexport function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): ParseResult<T> {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return { success: true, value: value as T, rawValue: value };\n    }\n\n    const validationResult = safeValidateTypes({ value, schema });\n\n    return validationResult.success\n      ? { ...validationResult, rawValue: value }\n      : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isInstance(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    SecureJSON.parse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { z } from 'zod';\nimport { Validator, asValidator } from './validator';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns {T} - The typed object.\n */\nexport function validateTypes<T>({\n  value,\n  schema: inputSchema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}): T {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n\n  if (!result.success) {\n    throw TypeValidationError.wrap({ value, cause: result.error });\n  }\n\n  return result.value;\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeValidateTypes<T>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}):\n  | { success: true; value: T }\n  | { success: false; error: TypeValidationError } {\n  const validator = asValidator(schema);\n\n  try {\n    if (validator.validate == null) {\n      return { success: true, value: value as T };\n    }\n\n    const result = validator.validate(value);\n\n    if (result.success) {\n      return result;\n    }\n\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: result.error }),\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: error }),\n    };\n  }\n}\n", "import { z } from 'zod';\n\n/**\n * Used to mark validator functions so we can support both Zod and custom schemas.\n */\nexport const validatorSymbol = Symbol.for('vercel.ai.validator');\n\nexport type ValidationResult<OBJECT> =\n  | { success: true; value: OBJECT }\n  | { success: false; error: Error };\n\nexport type Validator<OBJECT = unknown> = {\n  /**\n   * Used to mark validator functions so we can support both Zod and custom schemas.\n   */\n  [validatorSymbol]: true;\n\n  /**\n   * Optional. Validates that the structure of a value matches this schema,\n   * and returns a typed version of the value if it does.\n   */\n  readonly validate?: (value: unknown) => ValidationResult<OBJECT>;\n};\n\n/**\n * Create a validator.\n *\n * @param validate A validation function for the schema.\n */\nexport function validator<OBJECT>(\n  validate?: undefined | ((value: unknown) => ValidationResult<OBJECT>),\n): Validator<OBJECT> {\n  return { [validatorSymbol]: true, validate };\n}\n\nexport function isValidator(value: unknown): value is Validator {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    validatorSymbol in value &&\n    value[validatorSymbol] === true &&\n    'validate' in value\n  );\n}\n\nexport function asValidator<OBJECT>(\n  value: Validator<OBJECT> | z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return isValidator(value) ? value : zodValidator(value);\n}\n\nexport function zodValidator<OBJECT>(\n  zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return validator(value => {\n    const result = zodSchema.safeParse(value);\n    return result.success\n      ? { success: true, value: result.data }\n      : { success: false, error: result.error };\n  });\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { safeValidateTypes } from './validate-types';\nimport { z } from 'zod';\n\nexport function parseProviderOptions<T>({\n  provider,\n  providerOptions,\n  schema,\n}: {\n  provider: string;\n  providerOptions: Record<string, unknown> | undefined;\n  schema: z.ZodSchema<T>;\n}): T | undefined {\n  if (providerOptions?.[provider] == null) {\n    return undefined;\n  }\n\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema,\n  });\n\n  if (!parsedProviderOptions.success) {\n    throw new InvalidArgumentError({\n      argument: 'providerOptions',\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error,\n    });\n  }\n\n  return parsedProviderOptions.value;\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failedResponse<PERSON>and<PERSON>,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postFormDataToApi = async <T>({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  formData: FormData;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers,\n    body: {\n      content: formData,\n      values: Object.fromEntries((formData as any).entries()),\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    // unwrap original error when fetch failed (for easier debugging):\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n\n      if (cause != null) {\n        // Failed to connect to server:\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true, // retry when network error\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "export type Resolvable<T> =\n  | T // Raw value\n  | Promise<T> // Promise of value\n  | (() => T) // Function returning value\n  | (() => Promise<T>); // Function returning promise of value\n\n/**\n * Resolves a value that could be a raw value, a Promise, a function returning a value,\n * or a function returning a Promise.\n */\nexport async function resolve<T>(value: Resolvable<T>): Promise<T> {\n  // If it's a function, call it to get the value/promise\n  if (typeof value === 'function') {\n    value = (value as Function)();\n  }\n\n  // Otherwise just resolve whatever we got (value or promise)\n  return Promise.resolve(value as T);\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport { ZodSchema } from 'zod';\nimport {\n  createEventSourceParserStream,\n  EventSourceChunk,\n} from './event-source-parser-stream';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { parseJSON, ParseResult, safeParseJSON } from './parse-json';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  rawValue?: unknown;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodSchema<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(createEventSourceParserStream())\n        .pipeThrough(\n          new TransformStream<EventSourceChunk, ParseResult<T>>({\n            transform({ data }, controller) {\n              // ignore the 'DONE' event that e.g. OpenAI sends:\n              if (data === '[DONE]') {\n                return;\n              }\n\n              controller.enqueue(\n                safeParseJSON({\n                  text: data,\n                  schema: chunkSchema,\n                }),\n              );\n            },\n          }),\n        ),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodSchema<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n      rawValue: parsedResult.rawValue,\n    };\n  };\n\nexport const createBinaryResponseHandler =\n  (): ResponseHandler<Uint8Array> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.body) {\n      throw new APICallError({\n        message: 'Response body is empty',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n      });\n    }\n\n    try {\n      const buffer = await response.arrayBuffer();\n      return {\n        responseHeaders,\n        value: new Uint8Array(buffer),\n      };\n    } catch (error) {\n      throw new APICallError({\n        message: 'Failed to read response as array buffer',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n        cause: error,\n      });\n    }\n  };\n\nexport const createStatusCodeErrorResponseHandler =\n  (): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n    const responseBody = await response.text();\n\n    return {\n      responseHeaders,\n      value: new APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues: requestBodyValues as Record<string, unknown>,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n      }),\n    };\n  };\n", "// btoa and atob need to be invoked as a function call, not as a method call.\n// Otherwise CloudFlare will throw a\n// \"TypeError: Illegal invocation: function called with incorrect this reference\"\nconst { btoa, atob } = globalThis;\n\nexport function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return btoa(latin1string);\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n"], "names": ["resolve", "TypeValidationError", "validator", "TypeValidationError", "InvalidArgumentError", "InvalidArgumentError", "APICallError", "getOriginalFetch", "APICallError", "APICallError", "APICallError"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AKAA,SAAS,4BAA4B;AACrC,SAAS,sBAAsB;AQI/B,OAAO,gBAAgB;AbLhB,SAAS,eAAA,GACX,OAAA,EACiC;IACpC,OAAO,QAAQ,MAAA,CACb,CAAC,iBAAiB,iBAAA,CAAoB;YACpC,GAAG,eAAA;YACH,GAAI,kBAAA,OAAA,iBAAkB,CAAC,CAAA;QACzB,CAAA,GACA,CAAC;AAEL;;ACHO,SAAS,qCACd,QAAA,EACmB;IACnB,OAAO,IAAI,eAAkB;QAAA;;;;;KAAA,GAO3B,MAAM,MAAK,UAAA,EAAY;YACrB,IAAI;gBACF,MAAM,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,GAAI,MAAM,SAAS,IAAA,CAAK;gBAC5C,IAAI,MAAM;oBACR,WAAW,KAAA,CAAM;gBACnB,OAAO;oBACL,WAAW,OAAA,CAAQ,KAAK;gBAC1B;YACF,EAAA,OAAS,OAAO;gBACd,WAAW,KAAA,CAAM,KAAK;YACxB;QACF;QAAA;;KAAA,GAIA,SAAS,EAAC;IACZ,CAAC;AACH;;AC7BA,eAAsB,MAAM,SAAA,EAA0C;IACpE,OAAO,aAAa,OAChB,QAAQ,OAAA,CAAQ,IAChB,IAAI,QAAQ,CAAAA,WAAW,WAAWA,UAAS,SAAS,CAAC;AAC3D;;ACFO,SAAS,gCAAgC;IAC9C,IAAI,SAAS;IACb,IAAI,QAA4B,KAAA;IAChC,IAAI,OAAiB,CAAC,CAAA;IACtB,IAAI,cAAkC,KAAA;IACtC,IAAI,QAA4B,KAAA;IAEhC,SAAS,UACP,IAAA,EACA,UAAA,EACA;QAEA,IAAI,SAAS,IAAI;YACf,cAAc,UAAU;YACxB;QACF;QAGA,IAAI,KAAK,UAAA,CAAW,GAAG,GAAG;YACxB;QACF;QAGA,MAAM,aAAa,KAAK,OAAA,CAAQ,GAAG;QACnC,IAAI,eAAe,CAAA,GAAI;YAErB,YAAY,MAAM,EAAE;YACpB;QACF;QAEA,MAAM,QAAQ,KAAK,KAAA,CAAM,GAAG,UAAU;QAEtC,MAAM,aAAa,aAAa;QAChC,MAAM,QACJ,aAAa,KAAK,MAAA,IAAU,IAAA,CAAK,UAAU,CAAA,KAAM,MAC7C,KAAK,KAAA,CAAM,aAAa,CAAC,IACzB,KAAK,KAAA,CAAM,UAAU;QAE3B,YAAY,OAAO,KAAK;IAC1B;IAEA,SAAS,cACP,UAAA,EACA;QACA,IAAI,KAAK,MAAA,GAAS,GAAG;YACnB,WAAW,OAAA,CAAQ;gBACjB;gBACA,MAAM,KAAK,IAAA,CAAK,IAAI;gBACpB,IAAI;gBACJ;YACF,CAAC;YAGD,OAAO,CAAC,CAAA;YACR,QAAQ,KAAA;YACR,QAAQ,KAAA;QACV;IACF;IAEA,SAAS,YAAY,KAAA,EAAe,KAAA,EAAe;QACjD,OAAQ,OAAO;YACb,KAAK;gBACH,QAAQ;gBACR;YACF,KAAK;gBACH,KAAK,IAAA,CAAK,KAAK;gBACf;YACF,KAAK;gBACH,cAAc;gBACd;YACF,KAAK;gBACH,MAAM,cAAc,SAAS,OAAO,EAAE;gBACtC,IAAI,CAAC,MAAM,WAAW,GAAG;oBACvB,QAAQ;gBACV;gBACA;QACJ;IACF;IAEA,OAAO,IAAI,gBAA0C;QACnD,WAAU,KAAA,EAAO,UAAA,EAAY;YAC3B,MAAM,EAAE,KAAA,EAAO,cAAA,CAAe,CAAA,GAAI,WAAW,QAAQ,KAAK;YAE1D,SAAS;YAGT,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;gBACrC,UAAU,KAAA,CAAM,CAAC,CAAA,EAAG,UAAU;YAChC;QACF;QAEA,OAAM,UAAA,EAAY;YAChB,UAAU,QAAQ,UAAU;YAC5B,cAAc,UAAU;QAC1B;IACF,CAAC;AACH;AAGA,SAAS,WAAW,MAAA,EAAgB,KAAA,EAAe;IACjD,MAAM,QAAuB,CAAC,CAAA;IAC9B,IAAI,cAAc;IAGlB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAU;QAClC,MAAM,OAAO,KAAA,CAAM,GAAG,CAAA;QAGtB,IAAI,SAAS,MAAM;YAEjB,MAAM,IAAA,CAAK,WAAW;YACtB,cAAc;QAChB,OAAA,IAAW,SAAS,MAAM;YACxB,MAAM,IAAA,CAAK,WAAW;YACtB,cAAc;YACd,IAAI,KAAA,CAAM,CAAC,CAAA,KAAM,MAAM;gBACrB;YACF;QACF,OAAO;YACL,eAAe;QACjB;IACF;IAEA,OAAO;QAAE;QAAO,gBAAgB;IAAY;AAC9C;;AC7HO,SAAS,uBACd,QAAA,EACwB;IACxB,MAAM,UAAkC,CAAC;IACzC,SAAS,OAAA,CAAQ,OAAA,CAAQ,CAAC,OAAO,QAAQ;QACvC,OAAA,CAAQ,GAAG,CAAA,GAAI;IACjB,CAAC;IACD,OAAO;AACT;;;ACAO,IAAM,oBAAoB,CAAC,EAChC,MAAA,EACA,MAAM,cAAc,EAAA,EACpB,WAAW,gEAAA,EACX,YAAY,GAAA,EACd,GAKI,CAAC,CAAA,KAAmC;IACtC,MAAM,aAAY,+KAAA,EAAe,UAAU,WAAW;IAEtD,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IAGA,IAAI,SAAS,QAAA,CAAS,SAAS,GAAG;QAChC,MAAM,wKAAI,uBAAA,CAAqB;YAC7B,UAAU;YACV,SAAS,CAAA,eAAA,EAAkB,SAAS,CAAA,oCAAA,EAAuC,QAAQ,CAAA,EAAA,CAAA;QACrF,CAAC;IACH;IAEA,OAAO,CAAA,OAAQ,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,IAAI,CAAC,EAAA;AACxD;AAYO,IAAM,aAAa,kBAAkB;;ACpDrC,SAAS,gBAAgB,KAAA,EAA4B;IAC1D,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAA;IACf;IAEA,OAAO,KAAK,SAAA,CAAU,KAAK;AAC7B;;;AETO,SAAS,uBACd,MAAA,EACmB;IACnB,OAAO,OAAO,WAAA,CACZ,OAAO,OAAA,CAAQ,MAAM,EAAE,MAAA,CAAO,CAAC,CAAC,MAAM,KAAK,CAAA,GAAM,SAAS,IAAI;AAElE;;ACXO,SAAS,aAAa,KAAA,EAAgC;IAC3D,OACE,iBAAiB,SAAA,CAChB,MAAM,IAAA,KAAS,gBAAgB,MAAM,IAAA,KAAS,cAAA;AAEnD;;AFGA,IAAM,mBAAmB,IAAM,WAAW,KAAA;AAEnC,IAAM,aAAa,OAAU,EAClC,GAAA,EACA,UAAU,CAAC,CAAA,EACX,yBAAA,EACA,qBAAA,EACA,WAAA,EACA,QAAQ,iBAAiB,CAAA,EAC3B,KAOM;IACJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS,uBAAuB,OAAO;YACvC,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,IAAI;YAKJ,IAAI;gBACF,mBAAmB,MAAM,sBAAsB;oBAC7C;oBACA;oBACA,mBAAmB,CAAC;gBACtB,CAAC;YACH,EAAA,OAAS,OAAO;gBACd,IAAI,aAAa,KAAK,yKAAK,eAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;gBAEA,MAAM,wKAAI,eAAA,CAAa;oBACrB,SAAS;oBACT,OAAO;oBACP,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,mBAAmB,CAAC;gBACtB,CAAC;YACH;YAEA,MAAM,iBAAiB,KAAA;QACzB;QAEA,IAAI;YACF,OAAO,MAAM,0BAA0B;gBACrC;gBACA;gBACA,mBAAmB,CAAC;YACtB,CAAC;QACH,EAAA,OAAS,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,aAAa,KAAK,yKAAK,eAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;YACF;YAEA,MAAM,wKAAI,eAAA,CAAa;gBACrB,SAAS;gBACT,OAAO;gBACP,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA,mBAAmB,CAAC;YACtB,CAAC;QACH;IACF,EAAA,OAAS,OAAO;QACd,IAAI,aAAa,KAAK,GAAG;YACvB,MAAM;QACR;QAEA,IAAI,iBAAiB,aAAa,MAAM,OAAA,KAAY,gBAAgB;YAClE,MAAM,QAAS,MAAc,KAAA;YAC7B,IAAI,SAAS,MAAM;gBACjB,MAAM,wKAAI,eAAA,CAAa;oBACrB,SAAS,CAAA,uBAAA,EAA0B,MAAM,OAAO,EAAA;oBAChD;oBACA;oBACA,aAAa;oBACb,mBAAmB,CAAC;gBACtB,CAAC;YACH;QACF;QAEA,MAAM;IACR;AACF;;AGxGO,SAAS,WAAW,EACzB,MAAA,EACA,uBAAA,EACA,sBAAsB,QAAA,EACtB,WAAA,EACF,EAKW;IACT,IAAI,OAAO,WAAW,UAAU;QAC9B,OAAO;IACT;IAEA,IAAI,UAAU,MAAM;QAClB,MAAM,wKAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,0BAAA,CAAA;QACzB,CAAC;IACH;IAEA,IAAI,OAAO,YAAY,aAAa;QAClC,MAAM,wKAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,wCAAA,EAA2C,mBAAmB,CAAA,wEAAA,CAAA;QACvF,CAAC;IACH;IAEA,SAAS,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAE5C,IAAI,UAAU,MAAM;QAClB,MAAM,wKAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,wCAAA,EAA2C,mBAAmB,CAAA,mBAAA,EAAsB,uBAAuB,CAAA,sBAAA,CAAA;QACpI,CAAC;IACH;IAEA,IAAI,OAAO,WAAW,UAAU;QAC9B,MAAM,IAAI,sLAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,4CAAA,EAA+C,uBAAuB,CAAA,sCAAA,CAAA;QAC/F,CAAC;IACH;IAEA,OAAO;AACT;;ACrCO,SAAS,oBAAoB,EAClC,YAAA,EACA,uBAAA,EACF,EAGuB;IACrB,IAAI,OAAO,iBAAiB,UAAU;QACpC,OAAO;IACT;IAEA,IAAI,gBAAgB,QAAQ,OAAO,YAAY,aAAa;QAC1D,OAAO,KAAA;IACT;IAEA,eAAe,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAElD,IAAI,gBAAgB,QAAQ,OAAO,iBAAiB,UAAU;QAC5D,OAAO,KAAA;IACT;IAEA,OAAO;AACT;;AClBO,SAAS,YAAY,EAC1B,YAAA,EACA,uBAAA,EACA,WAAA,EACA,WAAA,EACF,EAKW;IACT,IAAI,OAAO,iBAAiB,UAAU;QACpC,OAAO;IACT;IAEA,IAAI,gBAAgB,MAAM;QACxB,MAAM,wKAAI,mBAAA,CAAiB;YACzB,SAAS,GAAG,WAAW,CAAA,0BAAA,CAAA;QACzB,CAAC;IACH;IAEA,IAAI,OAAO,YAAY,aAAa;QAClC,MAAM,wKAAI,mBAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,wCAAA,EACQ,WAAW,CAAA,wEAAA,CAAA;QAErC,CAAC;IACH;IAEA,eAAe,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAElD,IAAI,gBAAgB,MAAM;QACxB,MAAM,wKAAI,mBAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,wCAAA,EACQ,WAAW,CAAA,mBAAA,EACvB,uBAAuB,CAAA,sBAAA,CAAA;QACrC,CAAC;IACH;IAEA,IAAI,OAAO,iBAAiB,UAAU;QACpC,MAAM,IAAI,uLAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,4CAAA,EACM,uBAAuB,CAAA,sCAAA,CAAA;QAC/C,CAAC;IACH;IAEA,OAAO;AACT;;;;;AGxDO,IAAM,kBAAkB,OAAO,GAAA,CAAI,qBAAqB;AAwBxD,SAAS,UACd,QAAA,EACmB;IACnB,OAAO;QAAE,CAAC,eAAe,CAAA,EAAG;QAAM;IAAS;AAC7C;AAEO,SAAS,YAAY,KAAA,EAAoC;IAC9D,OACE,OAAO,UAAU,YACjB,UAAU,QACV,mBAAmB,SACnB,KAAA,CAAM,eAAe,CAAA,KAAM,QAC3B,cAAc;AAElB;AAEO,SAAS,YACd,KAAA,EACmB;IACnB,OAAO,YAAY,KAAK,IAAI,QAAQ,aAAa,KAAK;AACxD;AAEO,SAAS,aACd,SAAA,EACmB;IACnB,OAAO,UAAU,CAAA,UAAS;QACxB,MAAM,SAAS,UAAU,SAAA,CAAU,KAAK;QACxC,OAAO,OAAO,OAAA,GACV;YAAE,SAAS;YAAM,OAAO,OAAO,IAAA;QAAK,IACpC;YAAE,SAAS;YAAO,OAAO,OAAO,KAAA;QAAM;IAC5C,CAAC;AACH;;AD/CO,SAAS,cAAiB,EAC/B,KAAA,EACA,QAAQ,WAAA,EACV,EAGM;IACJ,MAAM,SAAS,kBAAkB;QAAE;QAAO,QAAQ;IAAY,CAAC;IAE/D,IAAI,CAAC,OAAO,OAAA,EAAS;QACnB,MAAM,0LAAA,CAAoB,IAAA,CAAK;YAAE;YAAO,OAAO,OAAO,KAAA;QAAM,CAAC;IAC/D;IAEA,OAAO,OAAO,KAAA;AAChB;AAWO,SAAS,kBAAqB,EACnC,KAAA,EACA,MAAA,EACF,EAKmD;IACjD,MAAME,aAAY,YAAY,MAAM;IAEpC,IAAI;QACF,IAAIA,WAAU,QAAA,IAAY,MAAM;YAC9B,OAAO;gBAAE,SAAS;gBAAM;YAAkB;QAC5C;QAEA,MAAM,SAASA,WAAU,QAAA,CAAS,KAAK;QAEvC,IAAI,OAAO,OAAA,EAAS;YAClB,OAAO;QACT;QAEA,OAAO;YACL,SAAS;YACT,2KAAO,sBAAA,CAAoB,IAAA,CAAK;gBAAE;gBAAO,OAAO,OAAO,KAAA;YAAM,CAAC;QAChE;IACF,EAAA,OAAS,OAAO;QACd,OAAO;YACL,SAAS;YACT,2KAAO,sBAAA,CAAoB,IAAA,CAAK;gBAAE;gBAAO,OAAO;YAAM,CAAC;QACzD;IACF;AACF;;ADtCO,SAAS,UAAa,EAC3B,IAAA,EACA,MAAA,EACF,EAGM;IACJ,IAAI;QACF,MAAM,kKAAQ,WAAA,CAAW,KAAA,CAAM,IAAI;QAEnC,IAAI,UAAU,MAAM;YAClB,OAAO;QACT;QAEA,OAAO,cAAc;YAAE;YAAO;QAAO,CAAC;IACxC,EAAA,OAAS,OAAO;QACd,wKACE,iBAAA,CAAe,UAAA,CAAW,KAAK,KAC/BC,0LAAAA,CAAoB,UAAA,CAAW,KAAK,GACpC;YACA,MAAM;QACR;QAEA,MAAM,wKAAI,iBAAA,CAAe;YAAE;YAAM,OAAO;QAAM,CAAC;IACjD;AACF;AA4BO,SAAS,cAAiB,EAC/B,IAAA,EACA,MAAA,EACF,EAGmB;IACjB,IAAI;QACF,MAAM,mKAAQ,UAAA,CAAW,KAAA,CAAM,IAAI;QAEnC,IAAI,UAAU,MAAM;YAClB,OAAO;gBAAE,SAAS;gBAAM;gBAAmB,UAAU;YAAM;QAC7D;QAEA,MAAM,mBAAmB,kBAAkB;YAAE;YAAO;QAAO,CAAC;QAE5D,OAAO,iBAAiB,OAAA,GACpB;YAAE,GAAG,gBAAA;YAAkB,UAAU;QAAM,IACvC;IACN,EAAA,OAAS,OAAO;QACd,OAAO;YACL,SAAS;YACT,2KAAO,iBAAA,CAAe,UAAA,CAAW,KAAK,IAClC,QACA,wKAAI,iBAAA,CAAe;gBAAE;gBAAM,OAAO;YAAM,CAAC;QAC/C;IACF;AACF;AAEO,SAAS,eAAe,KAAA,EAAwB;IACrD,IAAI;QACF,0JAAA,CAAA,UAAA,CAAW,KAAA,CAAM,KAAK;QACtB,OAAO;IACT,EAAA,OAAQ,GAAA;QACN,OAAO;IACT;AACF;;AGrHO,SAAS,qBAAwB,EACtC,QAAA,EACA,eAAA,EACA,MAAA,EACF,EAIkB;IAChB,IAAA,CAAI,mBAAA,OAAA,KAAA,IAAA,eAAA,CAAkB,SAAA,KAAa,MAAM;QACvC,OAAO,KAAA;IACT;IAEA,MAAM,wBAAwB,kBAAkB;QAC9C,OAAO,eAAA,CAAgB,QAAQ,CAAA;QAC/B;IACF,CAAC;IAED,IAAI,CAAC,sBAAsB,OAAA,EAAS;QAClC,MAAM,IAAIE,2LAAAA,CAAqB;YAC7B,UAAU;YACV,SAAS,CAAA,QAAA,EAAW,QAAQ,CAAA,iBAAA,CAAA;YAC5B,OAAO,sBAAsB,KAAA;QAC/B,CAAC;IACH;IAEA,OAAO,sBAAsB,KAAA;AAC/B;;ACvBA,IAAME,oBAAmB,IAAM,WAAW,KAAA;AAEnC,IAAM,gBAAgB,OAAU,EACrC,GAAA,EACA,OAAA,EACA,IAAA,EACA,qBAAA,EACA,yBAAA,EACA,WAAA,EACA,KAAA,EACF,GASE,UAAU;QACR;QACA,SAAS;YACP,gBAAgB;YAChB,GAAG,OAAA;QACL;QACA,MAAM;YACJ,SAAS,KAAK,SAAA,CAAU,IAAI;YAC5B,QAAQ;QACV;QACA;QACA;QACA;QACA;IACF,CAAC;AAEI,IAAM,oBAAoB,OAAU,EACzC,GAAA,EACA,OAAA,EACA,QAAA,EACA,qBAAA,EACA,yBAAA,EACA,WAAA,EACA,KAAA,EACF,GASE,UAAU;QACR;QACA;QACA,MAAM;YACJ,SAAS;YACT,QAAQ,OAAO,WAAA,CAAa,SAAiB,OAAA,CAAQ,CAAC;QACxD;QACA;QACA;QACA;QACA;IACF,CAAC;AAEI,IAAM,YAAY,OAAU,EACjC,GAAA,EACA,UAAU,CAAC,CAAA,EACX,IAAA,EACA,yBAAA,EACA,qBAAA,EACA,WAAA,EACA,QAAQA,kBAAiB,CAAA,EAC3B,KAWM;IACJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS,uBAAuB,OAAO;YACvC,MAAM,KAAK,OAAA;YACX,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,IAAI;YAKJ,IAAI;gBACF,mBAAmB,MAAM,sBAAsB;oBAC7C;oBACA;oBACA,mBAAmB,KAAK,MAAA;gBAC1B,CAAC;YACH,EAAA,OAAS,OAAO;gBACd,IAAI,aAAa,KAAK,yKAAKC,eAAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;gBAEA,MAAM,wKAAIA,eAAAA,CAAa;oBACrB,SAAS;oBACT,OAAO;oBACP,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,mBAAmB,KAAK,MAAA;gBAC1B,CAAC;YACH;YAEA,MAAM,iBAAiB,KAAA;QACzB;QAEA,IAAI;YACF,OAAO,MAAM,0BAA0B;gBACrC;gBACA;gBACA,mBAAmB,KAAK,MAAA;YAC1B,CAAC;QACH,EAAA,OAAS,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,aAAa,KAAK,yKAAKA,eAAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;YACF;YAEA,MAAM,wKAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT,OAAO;gBACP,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA,mBAAmB,KAAK,MAAA;YAC1B,CAAC;QACH;IACF,EAAA,OAAS,OAAO;QACd,IAAI,aAAa,KAAK,GAAG;YACvB,MAAM;QACR;QAGA,IAAI,iBAAiB,aAAa,MAAM,OAAA,KAAY,gBAAgB;YAClE,MAAM,QAAS,MAAc,KAAA;YAE7B,IAAI,SAAS,MAAM;gBAEjB,MAAM,wKAAIA,eAAAA,CAAa;oBACrB,SAAS,CAAA,uBAAA,EAA0B,MAAM,OAAO,EAAA;oBAChD;oBACA;oBACA,mBAAmB,KAAK,MAAA;oBACxB,aAAa;gBACf,CAAC;YACH;QACF;QAEA,MAAM;IACR;AACF;;ACxKA,eAAsB,QAAW,KAAA,EAAkC;IAEjE,IAAI,OAAO,UAAU,YAAY;QAC/B,QAAS,MAAmB;IAC9B;IAGA,OAAO,QAAQ,OAAA,CAAQ,KAAU;AACnC;;ACCO,IAAM,iCACX,CAAI,EACF,WAAA,EACA,cAAA,EACA,WAAA,EACF,GAKA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QACzC,MAAM,kBAAkB,uBAAuB,QAAQ;QAGvD,IAAI,aAAa,IAAA,CAAK,MAAM,IAAI;YAC9B,OAAO;gBACL;gBACA,OAAO,IAAIE,mLAAAA,CAAa;oBACtB,SAAS,SAAS,UAAA;oBAClB;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc;gBAC7B,CAAC;YACH;QACF;QAGA,IAAI;YACF,MAAM,cAAc,UAAU;gBAC5B,MAAM;gBACN,QAAQ;YACV,CAAC;YAED,OAAO;gBACL;gBACA,OAAO,wKAAIA,eAAAA,CAAa;oBACtB,SAAS,eAAe,WAAW;oBACnC;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,MAAM;oBACN,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc,UAAU;gBACvC,CAAC;YACH;QACF,EAAA,OAAS,YAAY;YACnB,OAAO;gBACL;gBACA,OAAO,wKAAIA,eAAAA,CAAa;oBACtB,SAAS,SAAS,UAAA;oBAClB;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc;gBAC7B,CAAC;YACH;QACF;IACF;AAEK,IAAM,mCACX,CACE,cAEF,OAAO,EAAE,QAAA,CAAS,CAAA,KAA8B;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,SAAS,IAAA,IAAQ,MAAM;YACzB,MAAM,wKAAI,yBAAA,CAAuB,CAAC,CAAC;QACrC;QAEA,OAAO;YACL;YACA,OAAO,SAAS,IAAA,CACb,WAAA,CAAY,IAAI,kBAAkB,CAAC,EACnC,WAAA,CAAY,8BAA8B,CAAC,EAC3C,WAAA,CACC,IAAI,gBAAkD;gBACpD,WAAU,EAAE,IAAA,CAAK,CAAA,EAAG,UAAA,EAAY;oBAE9B,IAAI,SAAS,UAAU;wBACrB;oBACF;oBAEA,WAAW,OAAA,CACT,cAAc;wBACZ,MAAM;wBACN,QAAQ;oBACV,CAAC;gBAEL;YACF,CAAC;QAEP;IACF;AAEK,IAAM,kCACX,CACE,cAEF,OAAO,EAAE,QAAA,CAAS,CAAA,KAA8B;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,SAAS,IAAA,IAAQ,MAAM;YACzB,MAAM,uKAAI,0BAAA,CAAuB,CAAC,CAAC;QACrC;QAEA,IAAI,SAAS;QAEb,OAAO;YACL;YACA,OAAO,SAAS,IAAA,CAAK,WAAA,CAAY,IAAI,kBAAkB,CAAC,EAAE,WAAA,CACxD,IAAI,gBAAwC;gBAC1C,WAAU,SAAA,EAAW,UAAA,EAAY;oBAC/B,IAAI,UAAU,QAAA,CAAS,IAAI,GAAG;wBAC5B,WAAW,OAAA,CACT,cAAc;4BACZ,MAAM,SAAS;4BACf,QAAQ;wBACV,CAAC;wBAEH,SAAS;oBACX,OAAO;wBACL,UAAU;oBACZ;gBACF;YACF,CAAC;QAEL;IACF;AAEK,IAAM,4BACX,CAAI,iBACJ,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QAEzC,MAAM,eAAe,cAAc;YACjC,MAAM;YACN,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,aAAa,OAAA,EAAS;YACzB,MAAM,wKAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT,OAAO,aAAa,KAAA;gBACpB,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA;gBACA;YACF,CAAC;QACH;QAEA,OAAO;YACL;YACA,OAAO,aAAa,KAAA;YACpB,UAAU,aAAa,QAAA;QACzB;IACF;AAEK,IAAM,8BACX,IACA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,IAAA,EAAM;YAClB,MAAM,wKAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA,cAAc,KAAA;YAChB,CAAC;QACH;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,SAAS,WAAA,CAAY;YAC1C,OAAO;gBACL;gBACA,OAAO,IAAI,WAAW,MAAM;YAC9B;QACF,EAAA,OAAS,OAAO;YACd,MAAM,wKAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA,cAAc,KAAA;gBACd,OAAO;YACT,CAAC;QACH;IACF;AAEK,IAAM,uCACX,IACA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QACvD,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QAEzC,OAAO;YACL;YACA,OAAO,wKAAIA,eAAAA,CAAa;gBACtB,SAAS,SAAS,UAAA;gBAClB;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA;YACF,CAAC;QACH;IACF;;AC5OF,IAAM,EAAE,IAAA,EAAM,IAAA,CAAK,CAAA,GAAI;AAEhB,SAAS,0BAA0B,YAAA,EAAsB;IAC9D,MAAM,YAAY,aAAa,OAAA,CAAQ,MAAM,GAAG,EAAE,OAAA,CAAQ,MAAM,GAAG;IACnE,MAAM,eAAe,KAAK,SAAS;IACnC,OAAO,WAAW,IAAA,CAAK,cAAc,CAAA,OAAQ,KAAK,WAAA,CAAY,CAAC,CAAE;AACnE;AAEO,SAAS,0BAA0B,KAAA,EAA2B;IACnE,IAAI,eAAe;IAInB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;QACrC,gBAAgB,OAAO,aAAA,CAAc,KAAA,CAAM,CAAC,CAAC;IAC/C;IAEA,OAAO,KAAK,YAAY;AAC1B;;ACrBO,SAAS,qBAAqB,GAAA,EAAyB;IAC5D,OAAO,OAAA,OAAA,KAAA,IAAA,IAAK,OAAA,CAAQ,OAAO;AAC7B", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@ai-sdk/anthropic/src/anthropic-provider.ts", "turbopack:///[project]/node_modules/@ai-sdk/anthropic/src/anthropic-messages-language-model.ts", "turbopack:///[project]/node_modules/@ai-sdk/anthropic/src/anthropic-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/anthropic/src/anthropic-prepare-tools.ts", "turbopack:///[project]/node_modules/@ai-sdk/anthropic/src/convert-to-anthropic-messages-prompt.ts", "turbopack:///[project]/node_modules/@ai-sdk/anthropic/src/map-anthropic-stop-reason.ts", "turbopack:///[project]/node_modules/@ai-sdk/anthropic/src/anthropic-tools.ts"], "sourcesContent": ["import {\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { AnthropicMessagesLanguageModel } from './anthropic-messages-language-model';\nimport {\n  AnthropicMessagesModelId,\n  AnthropicMessagesSettings,\n} from './anthropic-messages-settings';\nimport { anthropicTools } from './anthropic-tools';\n\nexport interface AnthropicProvider extends ProviderV1 {\n  /**\nCreates a model for text generation.\n*/\n  (\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a model for text generation.\n*/\n  languageModel(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\n@deprecated Use `.languageModel()` instead.\n*/\n  chat(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\n@deprecated Use `.languageModel()` instead.\n   */\n  messages(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\nAnthropic-specific computer use tool.\n   */\n  tools: typeof anthropicTools;\n}\n\nexport interface AnthropicProviderSettings {\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://api.anthropic.com/v1`.\n   */\n  baseURL?: string;\n\n  /**\nAPI key that is being send using the `x-api-key` header.\nIt defaults to the `ANTHROPIC_API_KEY` environment variable.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  generateId?: () => string;\n}\n\n/**\nCreate an Anthropic provider instance.\n */\nexport function createAnthropic(\n  options: AnthropicProviderSettings = {},\n): AnthropicProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ?? 'https://api.anthropic.com/v1';\n\n  const getHeaders = () => ({\n    'anthropic-version': '2023-06-01',\n    'x-api-key': loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'ANTHROPIC_API_KEY',\n      description: 'Anthropic',\n    }),\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: AnthropicMessagesModelId,\n    settings: AnthropicMessagesSettings = {},\n  ) =>\n    new AnthropicMessagesLanguageModel(modelId, settings, {\n      provider: 'anthropic.messages',\n      baseURL,\n      headers: getHeaders,\n      fetch: options.fetch,\n      supportsImageUrls: true,\n    });\n\n  const provider = function (\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Anthropic model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.messages = createChatModel;\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n\n  provider.tools = anthropicTools;\n\n  return provider;\n}\n\n/**\nDefault Anthropic provider instance.\n */\nexport const anthropic = createAnthropic();\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1FunctionToolCall,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  Resolvable,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  parseProviderOptions,\n  postJsonToApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { anthropicFailedResponseHandler } from './anthropic-error';\nimport {\n  AnthropicMessagesModelId,\n  AnthropicMessagesSettings,\n} from './anthropic-messages-settings';\nimport { prepareTools } from './anthropic-prepare-tools';\nimport { convertToAnthropicMessagesPrompt } from './convert-to-anthropic-messages-prompt';\nimport { mapAnthropicStopReason } from './map-anthropic-stop-reason';\n\ntype AnthropicMessagesConfig = {\n  provider: string;\n  baseURL: string;\n  headers: Resolvable<Record<string, string | undefined>>;\n  supportsImageUrls: boolean;\n  fetch?: FetchFunction;\n  buildRequestUrl?: (baseURL: string, isStreaming: boolean) => string;\n  transformRequestBody?: (args: Record<string, any>) => Record<string, any>;\n};\n\nexport class AnthropicMessagesLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'tool';\n\n  readonly modelId: AnthropicMessagesModelId;\n  readonly settings: AnthropicMessagesSettings;\n\n  private readonly config: AnthropicMessagesConfig;\n\n  constructor(\n    modelId: AnthropicMessagesModelId,\n    settings: AnthropicMessagesSettings,\n    config: AnthropicMessagesConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  supportsUrl(url: URL): boolean {\n    return url.protocol === 'https:';\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get supportsImageUrls(): boolean {\n    return this.config.supportsImageUrls;\n  }\n\n  private async getArgs({\n    mode,\n    prompt,\n    maxTokens = 4096, // 4096: max model output tokens TODO update default in v5\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata: providerOptions,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'frequencyPenalty',\n      });\n    }\n\n    if (presencePenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'presencePenalty',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    if (responseFormat != null && responseFormat.type !== 'text') {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details: 'JSON response format is not supported.',\n      });\n    }\n\n    const { prompt: messagesPrompt, betas: messagesBetas } =\n      convertToAnthropicMessagesPrompt({\n        prompt,\n        sendReasoning: this.settings.sendReasoning ?? true,\n        warnings,\n      });\n\n    const anthropicOptions = parseProviderOptions({\n      provider: 'anthropic',\n      providerOptions,\n      schema: anthropicProviderOptionsSchema,\n    });\n\n    const isThinking = anthropicOptions?.thinking?.type === 'enabled';\n    const thinkingBudget = anthropicOptions?.thinking?.budgetTokens;\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_k: topK,\n      top_p: topP,\n      stop_sequences: stopSequences,\n\n      // provider specific settings:\n      ...(isThinking && {\n        thinking: { type: 'enabled', budget_tokens: thinkingBudget },\n      }),\n\n      // prompt:\n      system: messagesPrompt.system,\n      messages: messagesPrompt.messages,\n    };\n\n    if (isThinking) {\n      if (thinkingBudget == null) {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'thinking requires a budget',\n        });\n      }\n\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'temperature',\n          details: 'temperature is not supported when thinking is enabled',\n        });\n      }\n\n      if (topK != null) {\n        baseArgs.top_k = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topK',\n          details: 'topK is not supported when thinking is enabled',\n        });\n      }\n\n      if (topP != null) {\n        baseArgs.top_p = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topP',\n          details: 'topP is not supported when thinking is enabled',\n        });\n      }\n\n      // adjust max tokens to account for thinking:\n      baseArgs.max_tokens = maxTokens + thinkingBudget;\n    }\n\n    switch (type) {\n      case 'regular': {\n        const {\n          tools,\n          tool_choice,\n          toolWarnings,\n          betas: toolsBetas,\n        } = prepareTools(mode);\n\n        return {\n          args: { ...baseArgs, tools, tool_choice },\n          warnings: [...warnings, ...toolWarnings],\n          betas: new Set([...messagesBetas, ...toolsBetas]),\n        };\n      }\n\n      case 'object-json': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'json-mode object generation',\n        });\n      }\n\n      case 'object-tool': {\n        const { name, description, parameters } = mode.tool;\n\n        return {\n          args: {\n            ...baseArgs,\n            tools: [{ name, description, input_schema: parameters }],\n            tool_choice: { type: 'tool', name },\n          },\n          warnings,\n          betas: messagesBetas,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  private async getHeaders({\n    betas,\n    headers,\n  }: {\n    betas: Set<string>;\n    headers: Record<string, string | undefined> | undefined;\n  }) {\n    return combineHeaders(\n      await resolve(this.config.headers),\n      betas.size > 0 ? { 'anthropic-beta': Array.from(betas).join(',') } : {},\n      headers,\n    );\n  }\n\n  private buildRequestUrl(isStreaming: boolean): string {\n    return (\n      this.config.buildRequestUrl?.(this.config.baseURL, isStreaming) ??\n      `${this.config.baseURL}/messages`\n    );\n  }\n\n  private transformRequestBody(args: Record<string, any>): Record<string, any> {\n    return this.config.transformRequestBody?.(args) ?? args;\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings, betas } = await this.getArgs(options);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.buildRequestUrl(false),\n      headers: await this.getHeaders({ betas, headers: options.headers }),\n      body: this.transformRequestBody(args),\n      failedResponseHandler: anthropicFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        anthropicMessagesResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    // extract text\n    let text = '';\n    for (const content of response.content) {\n      if (content.type === 'text') {\n        text += content.text;\n      }\n    }\n\n    // extract tool calls\n    let toolCalls: LanguageModelV1FunctionToolCall[] | undefined = undefined;\n    if (response.content.some(content => content.type === 'tool_use')) {\n      toolCalls = [];\n      for (const content of response.content) {\n        if (content.type === 'tool_use') {\n          toolCalls.push({\n            toolCallType: 'function',\n            toolCallId: content.id,\n            toolName: content.name,\n            args: JSON.stringify(content.input),\n          });\n        }\n      }\n    }\n\n    const reasoning = response.content\n      .filter(\n        content =>\n          content.type === 'redacted_thinking' || content.type === 'thinking',\n      )\n      .map(content =>\n        content.type === 'thinking'\n          ? {\n              type: 'text' as const,\n              text: content.thinking,\n              signature: content.signature,\n            }\n          : {\n              type: 'redacted' as const,\n              data: content.data,\n            },\n      );\n\n    return {\n      text,\n      reasoning: reasoning.length > 0 ? reasoning : undefined,\n      toolCalls,\n      finishReason: mapAnthropicStopReason(response.stop_reason),\n      usage: {\n        promptTokens: response.usage.input_tokens,\n        completionTokens: response.usage.output_tokens,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: {\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n      response: {\n        id: response.id ?? undefined,\n        modelId: response.model ?? undefined,\n      },\n      warnings,\n      providerMetadata: {\n        anthropic: {\n          cacheCreationInputTokens:\n            response.usage.cache_creation_input_tokens ?? null,\n          cacheReadInputTokens: response.usage.cache_read_input_tokens ?? null,\n        },\n      },\n      request: { body: JSON.stringify(args) },\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings, betas } = await this.getArgs(options);\n    const body = { ...args, stream: true };\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.buildRequestUrl(true),\n      headers: await this.getHeaders({ betas, headers: options.headers }),\n      body: this.transformRequestBody(body),\n      failedResponseHandler: anthropicFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        anthropicMessagesChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    const usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n\n    const toolCallContentBlocks: Record<\n      number,\n      {\n        toolCallId: string;\n        toolName: string;\n        jsonText: string;\n      }\n    > = {};\n\n    let providerMetadata: LanguageModelV1ProviderMetadata | undefined =\n      undefined;\n\n    let blockType:\n      | 'text'\n      | 'thinking'\n      | 'tool_use'\n      | 'redacted_thinking'\n      | undefined = undefined;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof anthropicMessagesChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            switch (value.type) {\n              case 'ping': {\n                return; // ignored\n              }\n\n              case 'content_block_start': {\n                const contentBlockType = value.content_block.type;\n\n                blockType = contentBlockType;\n\n                switch (contentBlockType) {\n                  case 'text':\n                  case 'thinking': {\n                    return; // ignored\n                  }\n\n                  case 'redacted_thinking': {\n                    controller.enqueue({\n                      type: 'redacted-reasoning',\n                      data: value.content_block.data,\n                    });\n                    return;\n                  }\n\n                  case 'tool_use': {\n                    toolCallContentBlocks[value.index] = {\n                      toolCallId: value.content_block.id,\n                      toolName: value.content_block.name,\n                      jsonText: '',\n                    };\n                    return;\n                  }\n\n                  default: {\n                    const _exhaustiveCheck: never = contentBlockType;\n                    throw new Error(\n                      `Unsupported content block type: ${_exhaustiveCheck}`,\n                    );\n                  }\n                }\n              }\n\n              case 'content_block_stop': {\n                // when finishing a tool call block, send the full tool call:\n                if (toolCallContentBlocks[value.index] != null) {\n                  const contentBlock = toolCallContentBlocks[value.index];\n\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallType: 'function',\n                    toolCallId: contentBlock.toolCallId,\n                    toolName: contentBlock.toolName,\n                    args: contentBlock.jsonText,\n                  });\n\n                  delete toolCallContentBlocks[value.index];\n                }\n\n                blockType = undefined; // reset block type\n\n                return;\n              }\n\n              case 'content_block_delta': {\n                const deltaType = value.delta.type;\n                switch (deltaType) {\n                  case 'text_delta': {\n                    controller.enqueue({\n                      type: 'text-delta',\n                      textDelta: value.delta.text,\n                    });\n\n                    return;\n                  }\n\n                  case 'thinking_delta': {\n                    controller.enqueue({\n                      type: 'reasoning',\n                      textDelta: value.delta.thinking,\n                    });\n\n                    return;\n                  }\n\n                  case 'signature_delta': {\n                    // signature are only supported on thinking blocks:\n                    if (blockType === 'thinking') {\n                      controller.enqueue({\n                        type: 'reasoning-signature',\n                        signature: value.delta.signature,\n                      });\n                    }\n\n                    return;\n                  }\n\n                  case 'input_json_delta': {\n                    const contentBlock = toolCallContentBlocks[value.index];\n\n                    controller.enqueue({\n                      type: 'tool-call-delta',\n                      toolCallType: 'function',\n                      toolCallId: contentBlock.toolCallId,\n                      toolName: contentBlock.toolName,\n                      argsTextDelta: value.delta.partial_json,\n                    });\n\n                    contentBlock.jsonText += value.delta.partial_json;\n\n                    return;\n                  }\n\n                  default: {\n                    const _exhaustiveCheck: never = deltaType;\n                    throw new Error(\n                      `Unsupported delta type: ${_exhaustiveCheck}`,\n                    );\n                  }\n                }\n              }\n\n              case 'message_start': {\n                usage.promptTokens = value.message.usage.input_tokens;\n                usage.completionTokens = value.message.usage.output_tokens;\n\n                providerMetadata = {\n                  anthropic: {\n                    cacheCreationInputTokens:\n                      value.message.usage.cache_creation_input_tokens ?? null,\n                    cacheReadInputTokens:\n                      value.message.usage.cache_read_input_tokens ?? null,\n                  },\n                };\n\n                controller.enqueue({\n                  type: 'response-metadata',\n                  id: value.message.id ?? undefined,\n                  modelId: value.message.model ?? undefined,\n                });\n\n                return;\n              }\n\n              case 'message_delta': {\n                usage.completionTokens = value.usage.output_tokens;\n                finishReason = mapAnthropicStopReason(value.delta.stop_reason);\n                return;\n              }\n\n              case 'message_stop': {\n                controller.enqueue({\n                  type: 'finish',\n                  finishReason,\n                  usage,\n                  providerMetadata,\n                });\n                return;\n              }\n\n              case 'error': {\n                controller.enqueue({ type: 'error', error: value.error });\n                return;\n              }\n\n              default: {\n                const _exhaustiveCheck: never = value;\n                throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);\n              }\n            }\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) },\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst anthropicMessagesResponseSchema = z.object({\n  type: z.literal('message'),\n  id: z.string().nullish(),\n  model: z.string().nullish(),\n  content: z.array(\n    z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('text'),\n        text: z.string(),\n      }),\n      z.object({\n        type: z.literal('thinking'),\n        thinking: z.string(),\n        signature: z.string(),\n      }),\n      z.object({\n        type: z.literal('redacted_thinking'),\n        data: z.string(),\n      }),\n      z.object({\n        type: z.literal('tool_use'),\n        id: z.string(),\n        name: z.string(),\n        input: z.unknown(),\n      }),\n    ]),\n  ),\n  stop_reason: z.string().nullish(),\n  usage: z.object({\n    input_tokens: z.number(),\n    output_tokens: z.number(),\n    cache_creation_input_tokens: z.number().nullish(),\n    cache_read_input_tokens: z.number().nullish(),\n  }),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst anthropicMessagesChunkSchema = z.discriminatedUnion('type', [\n  z.object({\n    type: z.literal('message_start'),\n    message: z.object({\n      id: z.string().nullish(),\n      model: z.string().nullish(),\n      usage: z.object({\n        input_tokens: z.number(),\n        output_tokens: z.number(),\n        cache_creation_input_tokens: z.number().nullish(),\n        cache_read_input_tokens: z.number().nullish(),\n      }),\n    }),\n  }),\n  z.object({\n    type: z.literal('content_block_start'),\n    index: z.number(),\n    content_block: z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('text'),\n        text: z.string(),\n      }),\n      z.object({\n        type: z.literal('thinking'),\n        thinking: z.string(),\n      }),\n      z.object({\n        type: z.literal('tool_use'),\n        id: z.string(),\n        name: z.string(),\n      }),\n      z.object({\n        type: z.literal('redacted_thinking'),\n        data: z.string(),\n      }),\n    ]),\n  }),\n  z.object({\n    type: z.literal('content_block_delta'),\n    index: z.number(),\n    delta: z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('input_json_delta'),\n        partial_json: z.string(),\n      }),\n      z.object({\n        type: z.literal('text_delta'),\n        text: z.string(),\n      }),\n      z.object({\n        type: z.literal('thinking_delta'),\n        thinking: z.string(),\n      }),\n      z.object({\n        type: z.literal('signature_delta'),\n        signature: z.string(),\n      }),\n    ]),\n  }),\n  z.object({\n    type: z.literal('content_block_stop'),\n    index: z.number(),\n  }),\n  z.object({\n    type: z.literal('error'),\n    error: z.object({\n      type: z.string(),\n      message: z.string(),\n    }),\n  }),\n  z.object({\n    type: z.literal('message_delta'),\n    delta: z.object({ stop_reason: z.string().nullish() }),\n    usage: z.object({ output_tokens: z.number() }),\n  }),\n  z.object({\n    type: z.literal('message_stop'),\n  }),\n  z.object({\n    type: z.literal('ping'),\n  }),\n]);\n\nconst anthropicProviderOptionsSchema = z.object({\n  thinking: z\n    .object({\n      type: z.union([z.literal('enabled'), z.literal('disabled')]),\n      budgetTokens: z.number().optional(),\n    })\n    .optional(),\n});\n\nexport type AnthropicProviderOptions = z.infer<\n  typeof anthropicProviderOptionsSchema\n>;\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\n\nconst anthropicErrorDataSchema = z.object({\n  type: z.literal('error'),\n  error: z.object({\n    type: z.string(),\n    message: z.string(),\n  }),\n});\n\nexport type AnthropicErrorData = z.infer<typeof anthropicErrorDataSchema>;\n\nexport const anthropicFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: anthropicErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { AnthropicTool, AnthropicToolChoice } from './anthropic-api-types';\n\nexport function prepareTools(\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  },\n): {\n  tools: Array<AnthropicTool> | undefined;\n  tool_choice: AnthropicToolChoice | undefined;\n  toolWarnings: LanguageModelV1CallWarning[];\n  betas: Set<string>;\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n  const betas = new Set<string>();\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined, toolWarnings, betas };\n  }\n\n  const anthropicTools: AnthropicTool[] = [];\n\n  for (const tool of tools) {\n    switch (tool.type) {\n      case 'function':\n        anthropicTools.push({\n          name: tool.name,\n          description: tool.description,\n          input_schema: tool.parameters,\n        });\n        break;\n      case 'provider-defined':\n        switch (tool.id) {\n          case 'anthropic.computer_20250124':\n            betas.add('computer-use-2025-01-24');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'computer_20250124',\n              display_width_px: tool.args.displayWidthPx as number,\n              display_height_px: tool.args.displayHeightPx as number,\n              display_number: tool.args.displayNumber as number,\n            });\n            break;\n          case 'anthropic.computer_20241022':\n            betas.add('computer-use-2024-10-22');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'computer_20241022',\n              display_width_px: tool.args.displayWidthPx as number,\n              display_height_px: tool.args.displayHeightPx as number,\n              display_number: tool.args.displayNumber as number,\n            });\n            break;\n          case 'anthropic.text_editor_20250124':\n            betas.add('computer-use-2025-01-24');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'text_editor_20250124',\n            });\n            break;\n          case 'anthropic.text_editor_20241022':\n            betas.add('computer-use-2024-10-22');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'text_editor_20241022',\n            });\n            break;\n          case 'anthropic.bash_20250124':\n            betas.add('computer-use-2025-01-24');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'bash_20250124',\n            });\n            break;\n          case 'anthropic.bash_20241022':\n            betas.add('computer-use-2024-10-22');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'bash_20241022',\n            });\n            break;\n          default:\n            toolWarnings.push({ type: 'unsupported-tool', tool });\n            break;\n        }\n        break;\n      default:\n        toolWarnings.push({ type: 'unsupported-tool', tool });\n        break;\n    }\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return {\n      tools: anthropicTools,\n      tool_choice: undefined,\n      toolWarnings,\n      betas,\n    };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n      return {\n        tools: anthropicTools,\n        tool_choice: { type: 'auto' },\n        toolWarnings,\n        betas,\n      };\n    case 'required':\n      return {\n        tools: anthropicTools,\n        tool_choice: { type: 'any' },\n        toolWarnings,\n        betas,\n      };\n    case 'none':\n      // Anthropic does not support 'none' tool choice, so we remove the tools:\n      return { tools: undefined, tool_choice: undefined, toolWarnings, betas };\n    case 'tool':\n      return {\n        tools: anthropicTools,\n        tool_choice: { type: 'tool', name: toolChoice.toolName },\n        toolWarnings,\n        betas,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import {\n  LanguageModelV1CallWarning,\n  LanguageModelV1Message,\n  LanguageModelV1Prompt,\n  LanguageModelV1ProviderMetadata,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport {\n  AnthropicAssistantMessage,\n  AnthropicCacheControl,\n  AnthropicMessagesPrompt,\n  AnthropicUserMessage,\n} from './anthropic-api-types';\n\nexport function convertToAnthropicMessagesPrompt({\n  prompt,\n  sendReasoning,\n  warnings,\n}: {\n  prompt: LanguageModelV1Prompt;\n  sendReasoning: boolean;\n  warnings: LanguageModelV1CallWarning[];\n}): {\n  prompt: AnthropicMessagesPrompt;\n  betas: Set<string>;\n} {\n  const betas = new Set<string>();\n  const blocks = groupIntoBlocks(prompt);\n\n  let system: AnthropicMessagesPrompt['system'] = undefined;\n  const messages: AnthropicMessagesPrompt['messages'] = [];\n\n  function getCacheControl(\n    providerMetadata: LanguageModelV1ProviderMetadata | undefined,\n  ): AnthropicCacheControl | undefined {\n    const anthropic = providerMetadata?.anthropic;\n\n    // allow both cacheControl and cache_control:\n    const cacheControlValue =\n      anthropic?.cacheControl ?? anthropic?.cache_control;\n\n    // Pass through value assuming it is of the correct type.\n    // The Anthropic API will validate the value.\n    return cacheControlValue as AnthropicCacheControl | undefined;\n  }\n\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i];\n    const isLastBlock = i === blocks.length - 1;\n    const type = block.type;\n\n    switch (type) {\n      case 'system': {\n        if (system != null) {\n          throw new UnsupportedFunctionalityError({\n            functionality:\n              'Multiple system messages that are separated by user/assistant messages',\n          });\n        }\n\n        system = block.messages.map(({ content, providerMetadata }) => ({\n          type: 'text',\n          text: content,\n          cache_control: getCacheControl(providerMetadata),\n        }));\n\n        break;\n      }\n\n      case 'user': {\n        // combines all user and tool messages in this block into a single message:\n        const anthropicContent: AnthropicUserMessage['content'] = [];\n\n        for (const message of block.messages) {\n          const { role, content } = message;\n          switch (role) {\n            case 'user': {\n              for (let j = 0; j < content.length; j++) {\n                const part = content[j];\n\n                // cache control: first add cache control from part.\n                // for the last part of a message,\n                // check also if the message has cache control.\n                const isLastPart = j === content.length - 1;\n\n                const cacheControl =\n                  getCacheControl(part.providerMetadata) ??\n                  (isLastPart\n                    ? getCacheControl(message.providerMetadata)\n                    : undefined);\n\n                switch (part.type) {\n                  case 'text': {\n                    anthropicContent.push({\n                      type: 'text',\n                      text: part.text,\n                      cache_control: cacheControl,\n                    });\n                    break;\n                  }\n\n                  case 'image': {\n                    anthropicContent.push({\n                      type: 'image',\n                      source:\n                        part.image instanceof URL\n                          ? {\n                              type: 'url',\n                              url: part.image.toString(),\n                            }\n                          : {\n                              type: 'base64',\n                              media_type: part.mimeType ?? 'image/jpeg',\n                              data: convertUint8ArrayToBase64(part.image),\n                            },\n                      cache_control: cacheControl,\n                    });\n\n                    break;\n                  }\n\n                  case 'file': {\n                    if (part.mimeType !== 'application/pdf') {\n                      throw new UnsupportedFunctionalityError({\n                        functionality: 'Non-PDF files in user messages',\n                      });\n                    }\n\n                    betas.add('pdfs-2024-09-25');\n\n                    anthropicContent.push({\n                      type: 'document',\n                      source:\n                        part.data instanceof URL\n                          ? {\n                              type: 'url',\n                              url: part.data.toString(),\n                            }\n                          : {\n                              type: 'base64',\n                              media_type: 'application/pdf',\n                              data: part.data,\n                            },\n                      cache_control: cacheControl,\n                    });\n\n                    break;\n                  }\n                }\n              }\n\n              break;\n            }\n            case 'tool': {\n              for (let i = 0; i < content.length; i++) {\n                const part = content[i];\n\n                // cache control: first add cache control from part.\n                // for the last part of a message,\n                // check also if the message has cache control.\n                const isLastPart = i === content.length - 1;\n\n                const cacheControl =\n                  getCacheControl(part.providerMetadata) ??\n                  (isLastPart\n                    ? getCacheControl(message.providerMetadata)\n                    : undefined);\n\n                const toolResultContent =\n                  part.content != null\n                    ? part.content.map(part => {\n                        switch (part.type) {\n                          case 'text':\n                            return {\n                              type: 'text' as const,\n                              text: part.text,\n                              cache_control: undefined,\n                            };\n                          case 'image':\n                            return {\n                              type: 'image' as const,\n                              source: {\n                                type: 'base64' as const,\n                                media_type: part.mimeType ?? 'image/jpeg',\n                                data: part.data,\n                              },\n                              cache_control: undefined,\n                            };\n                        }\n                      })\n                    : JSON.stringify(part.result);\n\n                anthropicContent.push({\n                  type: 'tool_result',\n                  tool_use_id: part.toolCallId,\n                  content: toolResultContent,\n                  is_error: part.isError,\n                  cache_control: cacheControl,\n                });\n              }\n\n              break;\n            }\n            default: {\n              const _exhaustiveCheck: never = role;\n              throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n\n        messages.push({ role: 'user', content: anthropicContent });\n\n        break;\n      }\n\n      case 'assistant': {\n        // combines multiple assistant messages in this block into a single message:\n        const anthropicContent: AnthropicAssistantMessage['content'] = [];\n\n        for (let j = 0; j < block.messages.length; j++) {\n          const message = block.messages[j];\n          const isLastMessage = j === block.messages.length - 1;\n          const { content } = message;\n\n          for (let k = 0; k < content.length; k++) {\n            const part = content[k];\n            const isLastContentPart = k === content.length - 1;\n\n            // cache control: first add cache control from part.\n            // for the last part of a message,\n            // check also if the message has cache control.\n            const cacheControl =\n              getCacheControl(part.providerMetadata) ??\n              (isLastContentPart\n                ? getCacheControl(message.providerMetadata)\n                : undefined);\n\n            switch (part.type) {\n              case 'text': {\n                anthropicContent.push({\n                  type: 'text',\n                  text:\n                    // trim the last text part if it's the last message in the block\n                    // because Anthropic does not allow trailing whitespace\n                    // in pre-filled assistant responses\n                    isLastBlock && isLastMessage && isLastContentPart\n                      ? part.text.trim()\n                      : part.text,\n\n                  cache_control: cacheControl,\n                });\n                break;\n              }\n\n              case 'reasoning': {\n                if (sendReasoning) {\n                  anthropicContent.push({\n                    type: 'thinking',\n                    thinking: part.text,\n                    signature: part.signature!,\n                    cache_control: cacheControl,\n                  });\n                } else {\n                  warnings.push({\n                    type: 'other',\n                    message:\n                      'sending reasoning content is disabled for this model',\n                  });\n                }\n                break;\n              }\n\n              case 'redacted-reasoning': {\n                anthropicContent.push({\n                  type: 'redacted_thinking',\n                  data: part.data,\n                  cache_control: cacheControl,\n                });\n                break;\n              }\n\n              case 'tool-call': {\n                anthropicContent.push({\n                  type: 'tool_use',\n                  id: part.toolCallId,\n                  name: part.toolName,\n                  input: part.args,\n                  cache_control: cacheControl,\n                });\n                break;\n              }\n            }\n          }\n        }\n\n        messages.push({ role: 'assistant', content: anthropicContent });\n\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return {\n    prompt: { system, messages },\n    betas,\n  };\n}\n\ntype SystemBlock = {\n  type: 'system';\n  messages: Array<LanguageModelV1Message & { role: 'system' }>;\n};\ntype AssistantBlock = {\n  type: 'assistant';\n  messages: Array<LanguageModelV1Message & { role: 'assistant' }>;\n};\ntype UserBlock = {\n  type: 'user';\n  messages: Array<LanguageModelV1Message & { role: 'user' | 'tool' }>;\n};\n\nfunction groupIntoBlocks(\n  prompt: LanguageModelV1Prompt,\n): Array<SystemBlock | AssistantBlock | UserBlock> {\n  const blocks: Array<SystemBlock | AssistantBlock | UserBlock> = [];\n  let currentBlock: SystemBlock | AssistantBlock | UserBlock | undefined =\n    undefined;\n\n  for (const message of prompt) {\n    const { role } = message;\n    switch (role) {\n      case 'system': {\n        if (currentBlock?.type !== 'system') {\n          currentBlock = { type: 'system', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'assistant': {\n        if (currentBlock?.type !== 'assistant') {\n          currentBlock = { type: 'assistant', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'user': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'tool': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return blocks;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapAnthropicStopReason(\n  finishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'end_turn':\n    case 'stop_sequence':\n      return 'stop';\n    case 'tool_use':\n      return 'tool-calls';\n    case 'max_tokens':\n      return 'length';\n    default:\n      return 'unknown';\n  }\n}\n", "import { z } from 'zod';\n\n// Copied from ai package\ntype ExecuteFunction<PARAMETERS, RESULT> =\n  | undefined\n  | ((\n      args: PARAMETERS,\n      options: { abortSignal?: AbortSignal },\n    ) => Promise<RESULT>);\n\n// Copied from ai package\nexport type ToolResultContent = Array<\n  | {\n      type: 'text';\n      text: string;\n    }\n  | {\n      type: 'image';\n      data: string; // base64 encoded png image, e.g. screenshot\n      mimeType?: string; // e.g. 'image/png';\n    }\n>;\n\nconst Bash20241022Parameters = z.object({\n  command: z.string(),\n  restart: z.boolean().optional(),\n});\n\n/**\n * Creates a tool for running a bash command. Must have name \"bash\".\n *\n * Image results are supported.\n *\n * @param execute - The function to execute the tool. Optional.\n */\nfunction bashTool_20241022<RESULT>(\n  options: {\n    execute?: ExecuteFunction<\n      {\n        /**\n         * The bash command to run. Required unless the tool is being restarted.\n         */\n        command: string;\n\n        /**\n         * Specifying true will restart this tool. Otherwise, leave this unspecified.\n         */\n        restart?: boolean;\n      },\n      RESULT\n    >;\n    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n  } = {},\n): {\n  type: 'provider-defined';\n  id: 'anthropic.bash_20241022';\n  args: {};\n  parameters: typeof Bash20241022Parameters;\n  execute: ExecuteFunction<z.infer<typeof Bash20241022Parameters>, RESULT>;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.bash_20241022',\n    args: {},\n    parameters: Bash20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst Bash20250124Parameters = z.object({\n  command: z.string(),\n  restart: z.boolean().optional(),\n});\n\n/**\n * Creates a tool for running a bash command. Must have name \"bash\".\n *\n * Image results are supported.\n *\n * @param execute - The function to execute the tool. Optional.\n */\nfunction bashTool_20250124<RESULT>(\n  options: {\n    execute?: ExecuteFunction<\n      {\n        /**\n         * The bash command to run. Required unless the tool is being restarted.\n         */\n        command: string;\n\n        /**\n         * Specifying true will restart this tool. Otherwise, leave this unspecified.\n         */\n        restart?: boolean;\n      },\n      RESULT\n    >;\n    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n  } = {},\n): {\n  type: 'provider-defined';\n  id: 'anthropic.bash_20250124';\n  args: {};\n  parameters: typeof Bash20250124Parameters;\n  execute: ExecuteFunction<z.infer<typeof Bash20250124Parameters>, RESULT>;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.bash_20250124',\n    args: {},\n    parameters: Bash20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst TextEditor20241022Parameters = z.object({\n  command: z.enum(['view', 'create', 'str_replace', 'insert', 'undo_edit']),\n  path: z.string(),\n  file_text: z.string().optional(),\n  insert_line: z.number().int().optional(),\n  new_str: z.string().optional(),\n  old_str: z.string().optional(),\n  view_range: z.array(z.number().int()).optional(),\n});\n\n/**\n * Creates a tool for editing text. Must have name \"str_replace_editor\".\n *\n * Image results are supported.\n *\n * @param execute - The function to execute the tool. Optional.\n */\nfunction textEditorTool_20241022<RESULT>(\n  options: {\n    execute?: ExecuteFunction<\n      {\n        /**\n         * The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.\n         */\n        command: 'view' | 'create' | 'str_replace' | 'insert' | 'undo_edit';\n\n        /**\n         * Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.\n         */\n        path: string;\n\n        /**\n         * Required parameter of `create` command, with the content of the file to be created.\n         */\n        file_text?: string;\n\n        /**\n         * Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.\n         */\n        insert_line?: number;\n\n        /**\n         * Optional parameter of `str_replace` command containing the new string (if not given, no string will be added). Required parameter of `insert` command containing the string to insert.\n         */\n        new_str?: string;\n\n        /**\n         * Required parameter of `str_replace` command containing the string in `path` to replace.\n         */\n        old_str?: string;\n\n        /**\n         * Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.\n         */\n        view_range?: number[];\n      },\n      RESULT\n    >;\n    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n  } = {},\n): {\n  type: 'provider-defined';\n  id: 'anthropic.text_editor_20241022';\n  args: {};\n  parameters: typeof TextEditor20241022Parameters;\n  execute: ExecuteFunction<\n    z.infer<typeof TextEditor20241022Parameters>,\n    RESULT\n  >;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.text_editor_20241022',\n    args: {},\n    parameters: TextEditor20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst TextEditor20250124Parameters = z.object({\n  command: z.enum(['view', 'create', 'str_replace', 'insert', 'undo_edit']),\n  path: z.string(),\n  file_text: z.string().optional(),\n  insert_line: z.number().int().optional(),\n  new_str: z.string().optional(),\n  old_str: z.string().optional(),\n  view_range: z.array(z.number().int()).optional(),\n});\n\n/**\n * Creates a tool for editing text. Must have name \"str_replace_editor\".\n *\n * Image results are supported.\n *\n * @param execute - The function to execute the tool. Optional.\n */\nfunction textEditorTool_20250124<RESULT>(\n  options: {\n    execute?: ExecuteFunction<\n      {\n        /**\n         * The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.\n         */\n        command: 'view' | 'create' | 'str_replace' | 'insert' | 'undo_edit';\n\n        /**\n         * Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.\n         */\n        path: string;\n\n        /**\n         * Required parameter of `create` command, with the content of the file to be created.\n         */\n        file_text?: string;\n\n        /**\n         * Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.\n         */\n        insert_line?: number;\n\n        /**\n         * Optional parameter of `str_replace` command containing the new string (if not given, no string will be added). Required parameter of `insert` command containing the string to insert.\n         */\n        new_str?: string;\n\n        /**\n         * Required parameter of `str_replace` command containing the string in `path` to replace.\n         */\n        old_str?: string;\n\n        /**\n         * Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.\n         */\n        view_range?: number[];\n      },\n      RESULT\n    >;\n    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n  } = {},\n): {\n  type: 'provider-defined';\n  id: 'anthropic.text_editor_20250124';\n  args: {};\n  parameters: typeof TextEditor20250124Parameters;\n  execute: ExecuteFunction<\n    z.infer<typeof TextEditor20250124Parameters>,\n    RESULT\n  >;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.text_editor_20250124',\n    args: {},\n    parameters: TextEditor20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst Computer20241022Parameters = z.object({\n  action: z.enum([\n    'key',\n    'type',\n    'mouse_move',\n    'left_click',\n    'left_click_drag',\n    'right_click',\n    'middle_click',\n    'double_click',\n    'screenshot',\n    'cursor_position',\n  ]),\n  coordinate: z.array(z.number().int()).optional(),\n  text: z.string().optional(),\n});\n\n/**\n * Creates a tool for executing actions on a computer. Must have name \"computer\".\n *\n * Image results are supported.\n *\n * @param displayWidthPx - The width of the display being controlled by the model in pixels.\n * @param displayHeightPx - The height of the display being controlled by the model in pixels.\n * @param displayNumber - The display number to control (only relevant for X11 environments). If specified, the tool will be provided a display number in the tool definition.\n * @param execute - The function to execute the tool. Optional.\n */\nfunction computerTool_20241022<RESULT>(options: {\n  displayWidthPx: number;\n  displayHeightPx: number;\n  displayNumber?: number;\n  execute?: ExecuteFunction<\n    {\n      /**\n       * The action to perform. The available actions are:\n       * - `key`: Press a key or key-combination on the keyboard.\n       *   - This supports xdotool's `key` syntax.\n       *   - Examples: \"a\", \"Return\", \"alt+Tab\", \"ctrl+s\", \"Up\", \"KP_0\" (for the numpad 0 key).\n       * - `type`: Type a string of text on the keyboard.\n       * - `cursor_position`: Get the current (x, y) pixel coordinate of the cursor on the screen.\n       * - `mouse_move`: Move the cursor to a specified (x, y) pixel coordinate on the screen.\n       * - `left_click`: Click the left mouse button.\n       * - `left_click_drag`: Click and drag the cursor to a specified (x, y) pixel coordinate on the screen.\n       * - `right_click`: Click the right mouse button.\n       * - `middle_click`: Click the middle mouse button.\n       * - `double_click`: Double-click the left mouse button.\n       * - `screenshot`: Take a screenshot of the screen.\n       */\n      action:\n        | 'key'\n        | 'type'\n        | 'mouse_move'\n        | 'left_click'\n        | 'left_click_drag'\n        | 'right_click'\n        | 'middle_click'\n        | 'double_click'\n        | 'screenshot'\n        | 'cursor_position';\n\n      /**\n       * (x, y): The x (pixels from the left edge) and y (pixels from the top edge) coordinates to move the mouse to. Required only by `action=mouse_move` and `action=left_click_drag`.\n       */\n      coordinate?: number[];\n\n      /**\n       * Required only by `action=type` and `action=key`.\n       */\n      text?: string;\n    },\n    RESULT\n  >;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n}): {\n  type: 'provider-defined';\n  id: 'anthropic.computer_20241022';\n  args: {};\n  parameters: typeof Computer20241022Parameters;\n  execute: ExecuteFunction<z.infer<typeof Computer20241022Parameters>, RESULT>;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.computer_20241022',\n    args: {\n      displayWidthPx: options.displayWidthPx,\n      displayHeightPx: options.displayHeightPx,\n      displayNumber: options.displayNumber,\n    },\n    parameters: Computer20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst Computer20250124Parameters = z.object({\n  action: z.enum([\n    'key',\n    'hold_key',\n    'type',\n    'cursor_position',\n    'mouse_move',\n    'left_mouse_down',\n    'left_mouse_up',\n    'left_click',\n    'left_click_drag',\n    'right_click',\n    'middle_click',\n    'double_click',\n    'triple_click',\n    'scroll',\n    'wait',\n    'screenshot',\n  ]),\n  coordinate: z.tuple([z.number().int(), z.number().int()]).optional(),\n  duration: z.number().optional(),\n  scroll_amount: z.number().optional(),\n  scroll_direction: z.enum(['up', 'down', 'left', 'right']).optional(),\n  start_coordinate: z.tuple([z.number().int(), z.number().int()]).optional(),\n  text: z.string().optional(),\n});\n\n/**\n * Creates a tool for executing actions on a computer. Must have name \"computer\".\n *\n * Image results are supported.\n *\n * @param displayWidthPx - The width of the display being controlled by the model in pixels.\n * @param displayHeightPx - The height of the display being controlled by the model in pixels.\n * @param displayNumber - The display number to control (only relevant for X11 environments). If specified, the tool will be provided a display number in the tool definition.\n * @param execute - The function to execute the tool. Optional.\n */\nfunction computerTool_20250124<RESULT>(options: {\n  displayWidthPx: number;\n  displayHeightPx: number;\n  displayNumber?: number;\n  execute?: ExecuteFunction<\n    {\n      /**\n       * - `key`: Press a key or key-combination on the keyboard.\n       *   - This supports xdotool's `key` syntax.\n       *   - Examples: \"a\", \"Return\", \"alt+Tab\", \"ctrl+s\", \"Up\", \"KP_0\" (for the numpad 0 key).\n       * - `hold_key`: Hold down a key or multiple keys for a specified duration (in seconds). Supports the same syntax as `key`.\n       * - `type`: Type a string of text on the keyboard.\n       * - `cursor_position`: Get the current (x, y) pixel coordinate of the cursor on the screen.\n       * - `mouse_move`: Move the cursor to a specified (x, y) pixel coordinate on the screen.\n       * - `left_mouse_down`: Press the left mouse button.\n       * - `left_mouse_up`: Release the left mouse button.\n       * - `left_click`: Click the left mouse button at the specified (x, y) pixel coordinate on the screen. You can also include a key combination to hold down while clicking using the `text` parameter.\n       * - `left_click_drag`: Click and drag the cursor from `start_coordinate` to a specified (x, y) pixel coordinate on the screen.\n       * - `right_click`: Click the right mouse button at the specified (x, y) pixel coordinate on the screen.\n       * - `middle_click`: Click the middle mouse button at the specified (x, y) pixel coordinate on the screen.\n       * - `double_click`: Double-click the left mouse button at the specified (x, y) pixel coordinate on the screen.\n       * - `triple_click`: Triple-click the left mouse button at the specified (x, y) pixel coordinate on the screen.\n       * - `scroll`: Scroll the screen in a specified direction by a specified amount of clicks of the scroll wheel, at the specified (x, y) pixel coordinate. DO NOT use PageUp/PageDown to scroll.\n       * - `wait`: Wait for a specified duration (in seconds).\n       * - `screenshot`: Take a screenshot of the screen.\n       */\n      action:\n        | 'key'\n        | 'hold_key'\n        | 'type'\n        | 'cursor_position'\n        | 'mouse_move'\n        | 'left_mouse_down'\n        | 'left_mouse_up'\n        | 'left_click'\n        | 'left_click_drag'\n        | 'right_click'\n        | 'middle_click'\n        | 'double_click'\n        | 'triple_click'\n        | 'scroll'\n        | 'wait'\n        | 'screenshot';\n\n      /**\n       * (x, y): The x (pixels from the left edge) and y (pixels from the top edge) coordinates to move the mouse to. Required only by `action=mouse_move` and `action=left_click_drag`.\n       */\n      coordinate?: [number, number];\n\n      /**\n       * The duration to hold the key down for. Required only by `action=hold_key` and `action=wait`.\n       */\n      duration?: number;\n\n      /**\n       * The number of 'clicks' to scroll. Required only by `action=scroll`.\n       */\n      scroll_amount?: number;\n\n      /**\n       * The direction to scroll the screen. Required only by `action=scroll`.\n       */\n      scroll_direction?: 'up' | 'down' | 'left' | 'right';\n\n      /**\n       * (x, y): The x (pixels from the left edge) and y (pixels from the top edge) coordinates to start the drag from. Required only by `action=left_click_drag`.\n       */\n      start_coordinate?: [number, number];\n\n      /**\n       * Required only by `action=type`, `action=key`, and `action=hold_key`. Can also be used by click or scroll actions to hold down keys while clicking or scrolling.\n       */\n      text?: string;\n    },\n    RESULT\n  >;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n}): {\n  type: 'provider-defined';\n  id: 'anthropic.computer_20250124';\n  args: {};\n  parameters: typeof Computer20250124Parameters;\n  execute: ExecuteFunction<z.infer<typeof Computer20250124Parameters>, RESULT>;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.computer_20250124',\n    args: {\n      displayWidthPx: options.displayWidthPx,\n      displayHeightPx: options.displayHeightPx,\n      displayNumber: options.displayNumber,\n    },\n    parameters: Computer20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nexport const anthropicTools = {\n  bash_20241022: bashTool_20241022,\n  bash_20250124: bashTool_20250124,\n  textEditor_20241022: textEditorTool_20241022,\n  textEditor_20250124: textEditorTool_20250124,\n  computer_20241022: computerTool_20241022,\n  computer_20250124: computerTool_20250124,\n};\n"], "names": ["UnsupportedFunctionalityError", "z", "anthropicTools", "UnsupportedFunctionalityError", "_a", "anthropic", "i", "part", "UnsupportedFunctionalityError", "z", "z"], "mappings": ";;;;;AAAA;AAKA;ACeA,SAAS,KAAAC,UAAS;;;;;;;;;ACjBlB,IAAM,2BAA2B,gMAAA,CAAE,MAAA,CAAO;IACxC,kMAAM,IAAA,CAAE,OAAA,CAAQ,OAAO;IACvB,mMAAO,IAAA,CAAE,MAAA,CAAO;QACd,kMAAM,IAAA,CAAE,MAAA,CAAO;QACf,oMAAS,KAAA,CAAE,MAAA,CAAO;IACpB,CAAC;AACH,CAAC;AAIM,IAAM,kNAAiC,iCAAA,EAA+B;IAC3E,aAAa;IACb,gBAAgB,CAAA,OAAQ,KAAK,KAAA,CAAM,OAAA;AACrC,CAAC;;ACTM,SAAS,aACd,IAAA,EAQA;IAhBF,IAAA;IAkBE,MAAM,QAAA,CAAA,CAAQ,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAY,MAAA,IAAS,KAAK,KAAA,GAAQ,KAAA;IAEhD,MAAM,eAA6C,CAAC,CAAA;IACpD,MAAM,QAAQ,aAAA,GAAA,IAAI,IAAY;IAE9B,IAAI,SAAS,MAAM;QACjB,OAAO;YAAE,OAAO,KAAA;YAAW,aAAa,KAAA;YAAW;YAAc;QAAM;IACzE;IAEA,MAAMC,kBAAkC,CAAC,CAAA;IAEzC,KAAA,MAAW,QAAQ,MAAO;QACxB,OAAQ,KAAK,IAAA,EAAM;YACjB,KAAK;gBACHA,gBAAe,IAAA,CAAK;oBAClB,MAAM,KAAK,IAAA;oBACX,aAAa,KAAK,WAAA;oBAClB,cAAc,KAAK,UAAA;gBACrB,CAAC;gBACD;YACF,KAAK;gBACH,OAAQ,KAAK,EAAA,EAAI;oBACf,KAAK;wBACH,MAAM,GAAA,CAAI,yBAAyB;wBACnCA,gBAAe,IAAA,CAAK;4BAClB,MAAM,KAAK,IAAA;4BACX,MAAM;4BACN,kBAAkB,KAAK,IAAA,CAAK,cAAA;4BAC5B,mBAAmB,KAAK,IAAA,CAAK,eAAA;4BAC7B,gBAAgB,KAAK,IAAA,CAAK,aAAA;wBAC5B,CAAC;wBACD;oBACF,KAAK;wBACH,MAAM,GAAA,CAAI,yBAAyB;wBACnCA,gBAAe,IAAA,CAAK;4BAClB,MAAM,KAAK,IAAA;4BACX,MAAM;4BACN,kBAAkB,KAAK,IAAA,CAAK,cAAA;4BAC5B,mBAAmB,KAAK,IAAA,CAAK,eAAA;4BAC7B,gBAAgB,KAAK,IAAA,CAAK,aAAA;wBAC5B,CAAC;wBACD;oBACF,KAAK;wBACH,MAAM,GAAA,CAAI,yBAAyB;wBACnCA,gBAAe,IAAA,CAAK;4BAClB,MAAM,KAAK,IAAA;4BACX,MAAM;wBACR,CAAC;wBACD;oBACF,KAAK;wBACH,MAAM,GAAA,CAAI,yBAAyB;wBACnCA,gBAAe,IAAA,CAAK;4BAClB,MAAM,KAAK,IAAA;4BACX,MAAM;wBACR,CAAC;wBACD;oBACF,KAAK;wBACH,MAAM,GAAA,CAAI,yBAAyB;wBACnCA,gBAAe,IAAA,CAAK;4BAClB,MAAM,KAAK,IAAA;4BACX,MAAM;wBACR,CAAC;wBACD;oBACF,KAAK;wBACH,MAAM,GAAA,CAAI,yBAAyB;wBACnCA,gBAAe,IAAA,CAAK;4BAClB,MAAM,KAAK,IAAA;4BACX,MAAM;wBACR,CAAC;wBACD;oBACF;wBACE,aAAa,IAAA,CAAK;4BAAE,MAAM;4BAAoB;wBAAK,CAAC;wBACpD;gBACJ;gBACA;YACF;gBACE,aAAa,IAAA,CAAK;oBAAE,MAAM;oBAAoB;gBAAK,CAAC;gBACpD;QACJ;IACF;IAEA,MAAM,aAAa,KAAK,UAAA;IAExB,IAAI,cAAc,MAAM;QACtB,OAAO;YACL,OAAOA;YACP,aAAa,KAAA;YACb;YACA;QACF;IACF;IAEA,MAAM,OAAO,WAAW,IAAA;IAExB,OAAQ,MAAM;QACZ,KAAK;YACH,OAAO;gBACL,OAAOA;gBACP,aAAa;oBAAE,MAAM;gBAAO;gBAC5B;gBACA;YACF;QACF,KAAK;YACH,OAAO;gBACL,OAAOA;gBACP,aAAa;oBAAE,MAAM;gBAAM;gBAC3B;gBACA;YACF;QACF,KAAK;YAEH,OAAO;gBAAE,OAAO,KAAA;gBAAW,aAAa,KAAA;gBAAW;gBAAc;YAAM;QACzE,KAAK;YACH,OAAO;gBACL,OAAOA;gBACP,aAAa;oBAAE,MAAM;oBAAQ,MAAM,WAAW,QAAA;gBAAS;gBACvD;gBACA;YACF;QACF;YAAS;gBACP,MAAM,mBAA0B;gBAChC,MAAM,wKAAI,gCAAA,CAA8B;oBACtC,eAAe,CAAA,8BAAA,EAAiC,gBAAgB,EAAA;gBAClE,CAAC;YACH;IACF;AACF;;;ACjIO,SAAS,iCAAiC,EAC/C,MAAA,EACA,aAAA,EACA,QAAA,EACF,EAOE;IA1BF,IAAA,IAAA,IAAA,IAAA;IA2BE,MAAM,QAAQ,aAAA,GAAA,IAAI,IAAY;IAC9B,MAAM,SAAS,gBAAgB,MAAM;IAErC,IAAI,SAA4C,KAAA;IAChD,MAAM,WAAgD,CAAC,CAAA;IAEvD,SAAS,gBACP,gBAAA,EACmC;QAnCvC,IAAAE;QAoCI,MAAMC,aAAY,oBAAA,OAAA,KAAA,IAAA,iBAAkB,SAAA;QAGpC,MAAM,oBAAA,CACJD,MAAAC,cAAA,OAAA,KAAA,IAAAA,WAAW,YAAA,KAAX,OAAAD,MAA2BC,cAAA,OAAA,KAAA,IAAAA,WAAW,aAAA;QAIxC,OAAO;IACT;IAEA,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,EAAQ,IAAK;QACtC,MAAM,QAAQ,MAAA,CAAO,CAAC,CAAA;QACtB,MAAM,cAAc,MAAM,OAAO,MAAA,GAAS;QAC1C,MAAM,OAAO,MAAM,IAAA;QAEnB,OAAQ,MAAM;YACZ,KAAK;gBAAU;oBACb,IAAI,UAAU,MAAM;wBAClB,MAAM,wKAAIF,gCAAAA,CAA8B;4BACtC,eACE;wBACJ,CAAC;oBACH;oBAEA,SAAS,MAAM,QAAA,CAAS,GAAA,CAAI,CAAC,EAAE,OAAA,EAAS,gBAAA,CAAiB,CAAA,GAAA,CAAO;4BAC9D,MAAM;4BACN,MAAM;4BACN,eAAe,gBAAgB,gBAAgB;wBACjD,CAAA,CAAE;oBAEF;gBACF;YAEA,KAAK;gBAAQ;oBAEX,MAAM,mBAAoD,CAAC,CAAA;oBAE3D,KAAA,MAAW,WAAW,MAAM,QAAA,CAAU;wBACpC,MAAM,EAAE,IAAA,EAAM,OAAA,CAAQ,CAAA,GAAI;wBAC1B,OAAQ,MAAM;4BACZ,KAAK;gCAAQ;oCACX,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAK;wCACvC,MAAM,OAAO,OAAA,CAAQ,CAAC,CAAA;wCAKtB,MAAM,aAAa,MAAM,QAAQ,MAAA,GAAS;wCAE1C,MAAM,eAAA,CACJ,KAAA,gBAAgB,KAAK,gBAAgB,CAAA,KAArC,OAAA,KACC,aACG,gBAAgB,QAAQ,gBAAgB,IACxC,KAAA;wCAEN,OAAQ,KAAK,IAAA,EAAM;4CACjB,KAAK;gDAAQ;oDACX,iBAAiB,IAAA,CAAK;wDACpB,MAAM;wDACN,MAAM,KAAK,IAAA;wDACX,eAAe;oDACjB,CAAC;oDACD;gDACF;4CAEA,KAAK;gDAAS;oDACZ,iBAAiB,IAAA,CAAK;wDACpB,MAAM;wDACN,QACE,KAAK,KAAA,YAAiB,MAClB;4DACE,MAAM;4DACN,KAAK,KAAK,KAAA,CAAM,QAAA,CAAS;wDAC3B,IACA;4DACE,MAAM;4DACN,YAAA,CAAY,KAAA,KAAK,QAAA,KAAL,OAAA,KAAiB;4DAC7B,MAAM,6MAAA,EAA0B,KAAK,KAAK;wDAC5C;wDACN,eAAe;oDACjB,CAAC;oDAED;gDACF;4CAEA,KAAK;gDAAQ;oDACX,IAAI,KAAK,QAAA,KAAa,mBAAmB;wDACvC,MAAM,wKAAIA,gCAAAA,CAA8B;4DACtC,eAAe;wDACjB,CAAC;oDACH;oDAEA,MAAM,GAAA,CAAI,iBAAiB;oDAE3B,iBAAiB,IAAA,CAAK;wDACpB,MAAM;wDACN,QACE,KAAK,IAAA,YAAgB,MACjB;4DACE,MAAM;4DACN,KAAK,KAAK,IAAA,CAAK,QAAA,CAAS;wDAC1B,IACA;4DACE,MAAM;4DACN,YAAY;4DACZ,MAAM,KAAK,IAAA;wDACb;wDACN,eAAe;oDACjB,CAAC;oDAED;gDACF;wCACF;oCACF;oCAEA;gCACF;4BACA,KAAK;gCAAQ;oCACX,IAAA,IAASG,KAAI,GAAGA,KAAI,QAAQ,MAAA,EAAQA,KAAK;wCACvC,MAAM,OAAO,OAAA,CAAQA,EAAC,CAAA;wCAKtB,MAAM,aAAaA,OAAM,QAAQ,MAAA,GAAS;wCAE1C,MAAM,eAAA,CACJ,KAAA,gBAAgB,KAAK,gBAAgB,CAAA,KAArC,OAAA,KACC,aACG,gBAAgB,QAAQ,gBAAgB,IACxC,KAAA;wCAEN,MAAM,oBACJ,KAAK,OAAA,IAAW,OACZ,KAAK,OAAA,CAAQ,GAAA,CAAI,CAAAC,UAAQ;4CA3K/C,IAAAH;4CA4KwB,OAAQG,MAAK,IAAA,EAAM;gDACjB,KAAK;oDACH,OAAO;wDACL,MAAM;wDACN,MAAMA,MAAK,IAAA;wDACX,eAAe,KAAA;oDACjB;gDACF,KAAK;oDACH,OAAO;wDACL,MAAM;wDACN,QAAQ;4DACN,MAAM;4DACN,YAAA,CAAYH,MAAAG,MAAK,QAAA,KAAL,OAAAH,MAAiB;4DAC7B,MAAMG,MAAK,IAAA;wDACb;wDACA,eAAe,KAAA;oDACjB;4CACJ;wCACF,CAAC,IACD,KAAK,SAAA,CAAU,KAAK,MAAM;wCAEhC,iBAAiB,IAAA,CAAK;4CACpB,MAAM;4CACN,aAAa,KAAK,UAAA;4CAClB,SAAS;4CACT,UAAU,KAAK,OAAA;4CACf,eAAe;wCACjB,CAAC;oCACH;oCAEA;gCACF;4BACA;gCAAS;oCACP,MAAM,mBAA0B;oCAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gCACzD;wBACF;oBACF;oBAEA,SAAS,IAAA,CAAK;wBAAE,MAAM;wBAAQ,SAAS;oBAAiB,CAAC;oBAEzD;gBACF;YAEA,KAAK;gBAAa;oBAEhB,MAAM,mBAAyD,CAAC,CAAA;oBAEhE,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,QAAA,CAAS,MAAA,EAAQ,IAAK;wBAC9C,MAAM,UAAU,MAAM,QAAA,CAAS,CAAC,CAAA;wBAChC,MAAM,gBAAgB,MAAM,MAAM,QAAA,CAAS,MAAA,GAAS;wBACpD,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI;wBAEpB,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAK;4BACvC,MAAM,OAAO,OAAA,CAAQ,CAAC,CAAA;4BACtB,MAAM,oBAAoB,MAAM,QAAQ,MAAA,GAAS;4BAKjD,MAAM,eAAA,CACJ,KAAA,gBAAgB,KAAK,gBAAgB,CAAA,KAArC,OAAA,KACC,oBACG,gBAAgB,QAAQ,gBAAgB,IACxC,KAAA;4BAEN,OAAQ,KAAK,IAAA,EAAM;gCACjB,KAAK;oCAAQ;wCACX,iBAAiB,IAAA,CAAK;4CACpB,MAAM;4CACN,MAAA,gEAAA;4CAAA,uDAAA;4CAAA,oCAAA;4CAIE,eAAe,iBAAiB,oBAC5B,KAAK,IAAA,CAAK,IAAA,CAAK,IACf,KAAK,IAAA;4CAEX,eAAe;wCACjB,CAAC;wCACD;oCACF;gCAEA,KAAK;oCAAa;wCAChB,IAAI,eAAe;4CACjB,iBAAiB,IAAA,CAAK;gDACpB,MAAM;gDACN,UAAU,KAAK,IAAA;gDACf,WAAW,KAAK,SAAA;gDAChB,eAAe;4CACjB,CAAC;wCACH,OAAO;4CACL,SAAS,IAAA,CAAK;gDACZ,MAAM;gDACN,SACE;4CACJ,CAAC;wCACH;wCACA;oCACF;gCAEA,KAAK;oCAAsB;wCACzB,iBAAiB,IAAA,CAAK;4CACpB,MAAM;4CACN,MAAM,KAAK,IAAA;4CACX,eAAe;wCACjB,CAAC;wCACD;oCACF;gCAEA,KAAK;oCAAa;wCAChB,iBAAiB,IAAA,CAAK;4CACpB,MAAM;4CACN,IAAI,KAAK,UAAA;4CACT,MAAM,KAAK,QAAA;4CACX,OAAO,KAAK,IAAA;4CACZ,eAAe;wCACjB,CAAC;wCACD;oCACF;4BACF;wBACF;oBACF;oBAEA,SAAS,IAAA,CAAK;wBAAE,MAAM;wBAAa,SAAS;oBAAiB,CAAC;oBAE9D;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,OAAO;QACL,QAAQ;YAAE;YAAQ;QAAS;QAC3B;IACF;AACF;AAeA,SAAS,gBACP,MAAA,EACiD;IACjD,MAAM,SAA0D,CAAC,CAAA;IACjE,IAAI,eACF,KAAA;IAEF,KAAA,MAAW,WAAW,OAAQ;QAC5B,MAAM,EAAE,IAAA,CAAK,CAAA,GAAI;QACjB,OAAQ,MAAM;YACZ,KAAK;gBAAU;oBACb,IAAA,CAAI,gBAAA,OAAA,KAAA,IAAA,aAAc,IAAA,MAAS,UAAU;wBACnC,eAAe;4BAAE,MAAM;4BAAU,UAAU,CAAC,CAAA;wBAAE;wBAC9C,OAAO,IAAA,CAAK,YAAY;oBAC1B;oBAEA,aAAa,QAAA,CAAS,IAAA,CAAK,OAAO;oBAClC;gBACF;YACA,KAAK;gBAAa;oBAChB,IAAA,CAAI,gBAAA,OAAA,KAAA,IAAA,aAAc,IAAA,MAAS,aAAa;wBACtC,eAAe;4BAAE,MAAM;4BAAa,UAAU,CAAC,CAAA;wBAAE;wBACjD,OAAO,IAAA,CAAK,YAAY;oBAC1B;oBAEA,aAAa,QAAA,CAAS,IAAA,CAAK,OAAO;oBAClC;gBACF;YACA,KAAK;gBAAQ;oBACX,IAAA,CAAI,gBAAA,OAAA,KAAA,IAAA,aAAc,IAAA,MAAS,QAAQ;wBACjC,eAAe;4BAAE,MAAM;4BAAQ,UAAU,CAAC,CAAA;wBAAE;wBAC5C,OAAO,IAAA,CAAK,YAAY;oBAC1B;oBAEA,aAAa,QAAA,CAAS,IAAA,CAAK,OAAO;oBAClC;gBACF;YACA,KAAK;gBAAQ;oBACX,IAAA,CAAI,gBAAA,OAAA,KAAA,IAAA,aAAc,IAAA,MAAS,QAAQ;wBACjC,eAAe;4BAAE,MAAM;4BAAQ,UAAU,CAAC,CAAA;wBAAE;wBAC5C,OAAO,IAAA,CAAK,YAAY;oBAC1B;oBAEA,aAAa,QAAA,CAAS,IAAA,CAAK,OAAO;oBAClC;gBACF;YACA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,OAAO;AACT;;AC3XO,SAAS,uBACd,YAAA,EAC6B;IAC7B,OAAQ,cAAc;QACpB,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;;AJwBO,IAAM,iCAAN,MAAgE;IASrE,YACE,OAAA,EACA,QAAA,EACA,MAAA,CACA;QAZF,IAAA,CAAS,oBAAA,GAAuB;QAChC,IAAA,CAAS,2BAAA,GAA8B;QAYrC,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,YAAY,GAAA,EAAmB;QAC7B,OAAO,IAAI,QAAA,KAAa;IAC1B;IAEA,IAAI,WAAmB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA;IACrB;IAEA,IAAI,oBAA6B;QAC/B,OAAO,IAAA,CAAK,MAAA,CAAO,iBAAA;IACrB;IAEA,MAAc,QAAQ,EACpB,IAAA,EACA,MAAA,EACA,YAAY,IAAA,EAAA,0DAAA;IACZ,WAAA,EACA,IAAA,EACA,IAAA,EACA,gBAAA,EACA,eAAA,EACA,aAAA,EACA,cAAA,EACA,IAAA,EACA,kBAAkB,eAAA,EACpB,EAAiD;QApFnD,IAAA,IAAA,IAAA;QAqFI,MAAM,OAAO,KAAK,IAAA;QAElB,MAAM,WAAyC,CAAC,CAAA;QAEhD,IAAI,oBAAoB,MAAM;YAC5B,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,mBAAmB,MAAM;YAC3B,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,QAAQ,MAAM;YAChB,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;YACX,CAAC;QACH;QAEA,IAAI,kBAAkB,QAAQ,eAAe,IAAA,KAAS,QAAQ;YAC5D,SAAS,IAAA,CAAK;gBACZ,MAAM;gBACN,SAAS;gBACT,SAAS;YACX,CAAC;QACH;QAEA,MAAM,EAAE,QAAQ,cAAA,EAAgB,OAAO,aAAA,CAAc,CAAA,GACnD,iCAAiC;YAC/B;YACA,eAAA,CAAe,KAAA,IAAA,CAAK,QAAA,CAAS,aAAA,KAAd,OAAA,KAA+B;YAC9C;QACF,CAAC;QAEH,MAAM,oMAAmB,uBAAA,EAAqB;YAC5C,UAAU;YACV;YACA,QAAQ;QACV,CAAC;QAED,MAAM,aAAA,CAAA,CAAa,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,QAAA,KAAlB,OAAA,KAAA,IAAA,GAA4B,IAAA,MAAS;QACxD,MAAM,iBAAA,CAAiB,KAAA,oBAAA,OAAA,KAAA,IAAA,iBAAkB,QAAA,KAAlB,OAAA,KAAA,IAAA,GAA4B,YAAA;QAEnD,MAAM,WAAW;YAAA,YAAA;YAEf,OAAO,IAAA,CAAK,OAAA;YAAA,yBAAA;YAGZ,YAAY;YACZ;YACA,OAAO;YACP,OAAO;YACP,gBAAgB;YAAA,8BAAA;YAGhB,GAAI,cAAc;gBAChB,UAAU;oBAAE,MAAM;oBAAW,eAAe;gBAAe;YAC7D,CAAA;YAAA,UAAA;YAGA,QAAQ,eAAe,MAAA;YACvB,UAAU,eAAe,QAAA;QAC3B;QAEA,IAAI,YAAY;YACd,IAAI,kBAAkB,MAAM;gBAC1B,MAAM,wKAAIC,gCAAAA,CAA8B;oBACtC,eAAe;gBACjB,CAAC;YACH;YAEA,IAAI,SAAS,WAAA,IAAe,MAAM;gBAChC,SAAS,WAAA,GAAc,KAAA;gBACvB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YAEA,IAAI,QAAQ,MAAM;gBAChB,SAAS,KAAA,GAAQ,KAAA;gBACjB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YAEA,IAAI,QAAQ,MAAM;gBAChB,SAAS,KAAA,GAAQ,KAAA;gBACjB,SAAS,IAAA,CAAK;oBACZ,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX,CAAC;YACH;YAGA,SAAS,UAAA,GAAa,YAAY;QACpC;QAEA,OAAQ,MAAM;YACZ,KAAK;gBAAW;oBACd,MAAM,EACJ,KAAA,EACA,WAAA,EACA,YAAA,EACA,OAAO,UAAA,EACT,GAAI,aAAa,IAAI;oBAErB,OAAO;wBACL,MAAM;4BAAE,GAAG,QAAA;4BAAU;4BAAO;wBAAY;wBACxC,UAAU,CAAC;+BAAG,UAAU;+BAAG,YAAY;yBAAA;wBACvC,OAAO,aAAA,GAAA,IAAI,IAAI,CAAC;+BAAG,eAAe;+BAAG,UAAU;yBAAC;oBAClD;gBACF;YAEA,KAAK;gBAAe;oBAClB,MAAM,wKAAIA,gCAAAA,CAA8B;wBACtC,eAAe;oBACjB,CAAC;gBACH;YAEA,KAAK;gBAAe;oBAClB,MAAM,EAAE,IAAA,EAAM,WAAA,EAAa,UAAA,CAAW,CAAA,GAAI,KAAK,IAAA;oBAE/C,OAAO;wBACL,MAAM;4BACJ,GAAG,QAAA;4BACH,OAAO;gCAAC;oCAAE;oCAAM;oCAAa,cAAc;gCAAW,CAAC;6BAAA;4BACvD,aAAa;gCAAE,MAAM;gCAAQ;4BAAK;wBACpC;wBACA;wBACA,OAAO;oBACT;gBACF;YAEA;gBAAS;oBACP,MAAM,mBAA0B;oBAChC,MAAM,IAAI,MAAM,CAAA,kBAAA,EAAqB,gBAAgB,EAAE;gBACzD;QACF;IACF;IAEA,MAAc,WAAW,EACvB,KAAA,EACA,OAAA,EACF,EAGG;QACD,wLAAO,iBAAA,EACL,uLAAM,UAAA,EAAQ,IAAA,CAAK,MAAA,CAAO,OAAO,GACjC,MAAM,IAAA,GAAO,IAAI;YAAE,kBAAkB,MAAM,IAAA,CAAK,KAAK,EAAE,IAAA,CAAK,GAAG;QAAE,IAAI,CAAC,GACtE;IAEJ;IAEQ,gBAAgB,WAAA,EAA8B;QA1PxD,IAAA,IAAA,IAAA;QA2PI,OAAA,CACE,KAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAK,MAAA,EAAO,eAAA,KAAZ,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAA8B,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS,YAAA,KAAnD,OAAA,KACA,GAAG,IAAA,CAAK,MAAA,CAAO,OAAO,CAAA,SAAA,CAAA;IAE1B;IAEQ,qBAAqB,IAAA,EAAgD;QAjQ/E,IAAA,IAAA,IAAA;QAkQI,OAAA,CAAO,KAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAK,MAAA,EAAO,oBAAA,KAAZ,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAmC,KAAA,KAAnC,OAAA,KAA4C;IACrD;IAEA,MAAM,WACJ,OAAA,EAC6D;QAvQjE,IAAA,IAAA,IAAA,IAAA;QAwQI,MAAM,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI,MAAM,IAAA,CAAK,OAAA,CAAQ,OAAO;QAE5D,MAAM,EACJ,eAAA,EACA,OAAO,QAAA,EACP,UAAU,WAAA,EACZ,GAAI,uLAAM,gBAAA,EAAc;YACtB,KAAK,IAAA,CAAK,eAAA,CAAgB,KAAK;YAC/B,SAAS,MAAM,IAAA,CAAK,UAAA,CAAW;gBAAE;gBAAO,SAAS,QAAQ,OAAA;YAAQ,CAAC;YAClE,MAAM,IAAA,CAAK,oBAAA,CAAqB,IAAI;YACpC,uBAAuB;YACvB,4MAA2B,4BAAA,EACzB;YAEF,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,EAAE,UAAU,SAAA,EAAW,GAAG,YAAY,CAAA,GAAI;QAGhD,IAAI,OAAO;QACX,KAAA,MAAW,WAAW,SAAS,OAAA,CAAS;YACtC,IAAI,QAAQ,IAAA,KAAS,QAAQ;gBAC3B,QAAQ,QAAQ,IAAA;YAClB;QACF;QAGA,IAAI,YAA2D,KAAA;QAC/D,IAAI,SAAS,OAAA,CAAQ,IAAA,CAAK,CAAA,UAAW,QAAQ,IAAA,KAAS,UAAU,GAAG;YACjE,YAAY,CAAC,CAAA;YACb,KAAA,MAAW,WAAW,SAAS,OAAA,CAAS;gBACtC,IAAI,QAAQ,IAAA,KAAS,YAAY;oBAC/B,UAAU,IAAA,CAAK;wBACb,cAAc;wBACd,YAAY,QAAQ,EAAA;wBACpB,UAAU,QAAQ,IAAA;wBAClB,MAAM,KAAK,SAAA,CAAU,QAAQ,KAAK;oBACpC,CAAC;gBACH;YACF;QACF;QAEA,MAAM,YAAY,SAAS,OAAA,CACxB,MAAA,CACC,CAAA,UACE,QAAQ,IAAA,KAAS,uBAAuB,QAAQ,IAAA,KAAS,YAE5D,GAAA,CAAI,CAAA,UACH,QAAQ,IAAA,KAAS,aACb;gBACE,MAAM;gBACN,MAAM,QAAQ,QAAA;gBACd,WAAW,QAAQ,SAAA;YACrB,IACA;gBACE,MAAM;gBACN,MAAM,QAAQ,IAAA;YAChB;QAGR,OAAO;YACL;YACA,WAAW,UAAU,MAAA,GAAS,IAAI,YAAY,KAAA;YAC9C;YACA,cAAc,uBAAuB,SAAS,WAAW;YACzD,OAAO;gBACL,cAAc,SAAS,KAAA,CAAM,YAAA;gBAC7B,kBAAkB,SAAS,KAAA,CAAM,aAAA;YACnC;YACA,SAAS;gBAAE;gBAAW;YAAY;YAClC,aAAa;gBACX,SAAS;gBACT,MAAM;YACR;YACA,UAAU;gBACR,IAAA,CAAI,KAAA,SAAS,EAAA,KAAT,OAAA,KAAe,KAAA;gBACnB,SAAA,CAAS,KAAA,SAAS,KAAA,KAAT,OAAA,KAAkB,KAAA;YAC7B;YACA;YACA,kBAAkB;gBAChB,WAAW;oBACT,0BAAA,CACE,KAAA,SAAS,KAAA,CAAM,2BAAA,KAAf,OAAA,KAA8C;oBAChD,sBAAA,CAAsB,KAAA,SAAS,KAAA,CAAM,uBAAA,KAAf,OAAA,KAA0C;gBAClE;YACF;YACA,SAAS;gBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;YAAE;QACxC;IACF;IAEA,MAAM,SACJ,OAAA,EAC2D;QAC3D,MAAM,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI,MAAM,IAAA,CAAK,OAAA,CAAQ,OAAO;QAC5D,MAAM,OAAO;YAAE,GAAG,IAAA;YAAM,QAAQ;QAAK;QAErC,MAAM,EAAE,eAAA,EAAiB,OAAO,QAAA,CAAS,CAAA,GAAI,uLAAM,gBAAA,EAAc;YAC/D,KAAK,IAAA,CAAK,eAAA,CAAgB,IAAI;YAC9B,SAAS,MAAM,IAAA,CAAK,UAAA,CAAW;gBAAE;gBAAO,SAAS,QAAQ,OAAA;YAAQ,CAAC;YAClE,MAAM,IAAA,CAAK,oBAAA,CAAqB,IAAI;YACpC,uBAAuB;YACvB,+BAA2B,gNAAA,EACzB;YAEF,aAAa,QAAQ,WAAA;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA;QACrB,CAAC;QAED,MAAM,EAAE,UAAU,SAAA,EAAW,GAAG,YAAY,CAAA,GAAI;QAEhD,IAAI,eAA4C;QAChD,MAAM,QAA4D;YAChE,cAAc,OAAO,GAAA;YACrB,kBAAkB,OAAO,GAAA;QAC3B;QAEA,MAAM,wBAOF,CAAC;QAEL,IAAI,mBACF,KAAA;QAEF,IAAI,YAKY,KAAA;QAEhB,OAAO;YACL,QAAQ,SAAS,WAAA,CACf,IAAI,gBAGF;gBACA,WAAU,KAAA,EAAO,UAAA,EAAY;oBAvZvC,IAAA,IAAA,IAAA,IAAA;oBAwZY,IAAI,CAAC,MAAM,OAAA,EAAS;wBAClB,WAAW,OAAA,CAAQ;4BAAE,MAAM;4BAAS,OAAO,MAAM,KAAA;wBAAM,CAAC;wBACxD;oBACF;oBAEA,MAAM,QAAQ,MAAM,KAAA;oBAEpB,OAAQ,MAAM,IAAA,EAAM;wBAClB,KAAK;4BAAQ;gCACX;4BACF;wBAEA,KAAK;4BAAuB;gCAC1B,MAAM,mBAAmB,MAAM,aAAA,CAAc,IAAA;gCAE7C,YAAY;gCAEZ,OAAQ,kBAAkB;oCACxB,KAAK;oCACL,KAAK;wCAAY;4CACf;wCACF;oCAEA,KAAK;wCAAqB;4CACxB,WAAW,OAAA,CAAQ;gDACjB,MAAM;gDACN,MAAM,MAAM,aAAA,CAAc,IAAA;4CAC5B,CAAC;4CACD;wCACF;oCAEA,KAAK;wCAAY;4CACf,qBAAA,CAAsB,MAAM,KAAK,CAAA,GAAI;gDACnC,YAAY,MAAM,aAAA,CAAc,EAAA;gDAChC,UAAU,MAAM,aAAA,CAAc,IAAA;gDAC9B,UAAU;4CACZ;4CACA;wCACF;oCAEA;wCAAS;4CACP,MAAM,mBAA0B;4CAChC,MAAM,IAAI,MACR,CAAA,gCAAA,EAAmC,gBAAgB,EAAA;wCAEvD;gCACF;4BACF;wBAEA,KAAK;4BAAsB;gCAEzB,IAAI,qBAAA,CAAsB,MAAM,KAAK,CAAA,IAAK,MAAM;oCAC9C,MAAM,eAAe,qBAAA,CAAsB,MAAM,KAAK,CAAA;oCAEtD,WAAW,OAAA,CAAQ;wCACjB,MAAM;wCACN,cAAc;wCACd,YAAY,aAAa,UAAA;wCACzB,UAAU,aAAa,QAAA;wCACvB,MAAM,aAAa,QAAA;oCACrB,CAAC;oCAED,OAAO,qBAAA,CAAsB,MAAM,KAAK,CAAA;gCAC1C;gCAEA,YAAY,KAAA;gCAEZ;4BACF;wBAEA,KAAK;4BAAuB;gCAC1B,MAAM,YAAY,MAAM,KAAA,CAAM,IAAA;gCAC9B,OAAQ,WAAW;oCACjB,KAAK;wCAAc;4CACjB,WAAW,OAAA,CAAQ;gDACjB,MAAM;gDACN,WAAW,MAAM,KAAA,CAAM,IAAA;4CACzB,CAAC;4CAED;wCACF;oCAEA,KAAK;wCAAkB;4CACrB,WAAW,OAAA,CAAQ;gDACjB,MAAM;gDACN,WAAW,MAAM,KAAA,CAAM,QAAA;4CACzB,CAAC;4CAED;wCACF;oCAEA,KAAK;wCAAmB;4CAEtB,IAAI,cAAc,YAAY;gDAC5B,WAAW,OAAA,CAAQ;oDACjB,MAAM;oDACN,WAAW,MAAM,KAAA,CAAM,SAAA;gDACzB,CAAC;4CACH;4CAEA;wCACF;oCAEA,KAAK;wCAAoB;4CACvB,MAAM,eAAe,qBAAA,CAAsB,MAAM,KAAK,CAAA;4CAEtD,WAAW,OAAA,CAAQ;gDACjB,MAAM;gDACN,cAAc;gDACd,YAAY,aAAa,UAAA;gDACzB,UAAU,aAAa,QAAA;gDACvB,eAAe,MAAM,KAAA,CAAM,YAAA;4CAC7B,CAAC;4CAED,aAAa,QAAA,IAAY,MAAM,KAAA,CAAM,YAAA;4CAErC;wCACF;oCAEA;wCAAS;4CACP,MAAM,mBAA0B;4CAChC,MAAM,IAAI,MACR,CAAA,wBAAA,EAA2B,gBAAgB,EAAA;wCAE/C;gCACF;4BACF;wBAEA,KAAK;4BAAiB;gCACpB,MAAM,YAAA,GAAe,MAAM,OAAA,CAAQ,KAAA,CAAM,YAAA;gCACzC,MAAM,gBAAA,GAAmB,MAAM,OAAA,CAAQ,KAAA,CAAM,aAAA;gCAE7C,mBAAmB;oCACjB,WAAW;wCACT,0BAAA,CACE,KAAA,MAAM,OAAA,CAAQ,KAAA,CAAM,2BAAA,KAApB,OAAA,KAAmD;wCACrD,sBAAA,CACE,KAAA,MAAM,OAAA,CAAQ,KAAA,CAAM,uBAAA,KAApB,OAAA,KAA+C;oCACnD;gCACF;gCAEA,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN,IAAA,CAAI,KAAA,MAAM,OAAA,CAAQ,EAAA,KAAd,OAAA,KAAoB,KAAA;oCACxB,SAAA,CAAS,KAAA,MAAM,OAAA,CAAQ,KAAA,KAAd,OAAA,KAAuB,KAAA;gCAClC,CAAC;gCAED;4BACF;wBAEA,KAAK;4BAAiB;gCACpB,MAAM,gBAAA,GAAmB,MAAM,KAAA,CAAM,aAAA;gCACrC,eAAe,uBAAuB,MAAM,KAAA,CAAM,WAAW;gCAC7D;4BACF;wBAEA,KAAK;4BAAgB;gCACnB,WAAW,OAAA,CAAQ;oCACjB,MAAM;oCACN;oCACA;oCACA;gCACF,CAAC;gCACD;4BACF;wBAEA,KAAK;4BAAS;gCACZ,WAAW,OAAA,CAAQ;oCAAE,MAAM;oCAAS,OAAO,MAAM,KAAA;gCAAM,CAAC;gCACxD;4BACF;wBAEA;4BAAS;gCACP,MAAM,mBAA0B;gCAChC,MAAM,IAAI,MAAM,CAAA,wBAAA,EAA2B,gBAAgB,EAAE;4BAC/D;oBACF;gBACF;YACF,CAAC;YAEH,SAAS;gBAAE;gBAAW;YAAY;YAClC,aAAa;gBAAE,SAAS;YAAgB;YACxC;YACA,SAAS;gBAAE,MAAM,KAAK,SAAA,CAAU,IAAI;YAAE;QACxC;IACF;AACF;AAIA,IAAM,8NAAkCC,IAAAA,CAAE,MAAA,CAAO;IAC/C,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,SAAS;IACzB,IAAIA,gMAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IACvB,mMAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC1B,qMAASA,IAAAA,CAAE,KAAA,6LACTA,IAAAA,CAAE,kBAAA,CAAmB,QAAQ;oMAC3BA,IAAAA,CAAE,MAAA,CAAO;YACP,MAAMA,gMAAAA,CAAE,OAAA,CAAQ,MAAM;YACtB,kMAAMA,IAAAA,CAAE,MAAA,CAAO;QACjB,CAAC;oMACDA,IAAAA,CAAE,MAAA,CAAO;YACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU;YAC1B,sMAAUA,IAAAA,CAAE,MAAA,CAAO;YACnB,uMAAWA,IAAAA,CAAE,MAAA,CAAO;QACtB,CAAC;oMACDA,IAAAA,CAAE,MAAA,CAAO;YACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,mBAAmB;YACnC,MAAMA,gMAAAA,CAAE,MAAA,CAAO;QACjB,CAAC;oMACDA,IAAAA,CAAE,MAAA,CAAO;YACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU;YAC1B,gMAAIA,IAAAA,CAAE,MAAA,CAAO;YACb,iMAAMA,KAAAA,CAAE,MAAA,CAAO;YACf,mMAAOA,IAAAA,CAAE,OAAA,CAAQ;QACnB,CAAC;KACF;IAEH,yMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAChC,mMAAOA,IAAAA,CAAE,MAAA,CAAO;QACd,0MAAcA,IAAAA,CAAE,MAAA,CAAO;QACvB,2MAAeA,IAAAA,CAAE,MAAA,CAAO;QACxB,yNAA6BA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAChD,yBAAyBA,gMAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;IAC9C,CAAC;AACH,CAAC;AAID,IAAM,2NAA+BA,IAAAA,CAAE,kBAAA,CAAmB,QAAQ;gMAChEA,IAAAA,CAAE,MAAA,CAAO;QACP,MAAMA,gMAAAA,CAAE,OAAA,CAAQ,eAAe;QAC/B,qMAASA,IAAAA,CAAE,MAAA,CAAO;YAChB,IAAIA,gMAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YACvB,mMAAOA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAC1B,mMAAOA,IAAAA,CAAE,MAAA,CAAO;gBACd,0MAAcA,IAAAA,CAAE,MAAA,CAAO;gBACvB,2MAAeA,IAAAA,CAAE,MAAA,CAAO;gBACxB,6BAA6BA,gMAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;gBAChD,qNAAyBA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;gMACDA,IAAAA,CAAE,MAAA,CAAO;QACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,qBAAqB;QACrC,mMAAOA,IAAAA,CAAE,MAAA,CAAO;QAChB,2MAAeA,IAAAA,CAAE,kBAAA,CAAmB,QAAQ;wMAC1CA,IAAAA,CAAE,MAAA,CAAO;gBACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,MAAM;gBACtB,MAAMA,gMAAAA,CAAE,MAAA,CAAO;YACjB,CAAC;wMACDA,IAAAA,CAAE,MAAA,CAAO;gBACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU;gBAC1B,sMAAUA,IAAAA,CAAE,MAAA,CAAO;YACrB,CAAC;wMACDA,IAAAA,CAAE,MAAA,CAAO;gBACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,UAAU;gBAC1B,gMAAIA,IAAAA,CAAE,MAAA,CAAO;gBACb,kMAAMA,IAAAA,CAAE,MAAA,CAAO;YACjB,CAAC;wMACDA,IAAAA,CAAE,MAAA,CAAO;gBACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,mBAAmB;gBACnC,kMAAMA,IAAAA,CAAE,MAAA,CAAO;YACjB,CAAC;SACF;IACH,CAAC;IACDA,gMAAAA,CAAE,MAAA,CAAO;QACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,qBAAqB;QACrC,mMAAOA,IAAAA,CAAE,MAAA,CAAO;QAChB,mMAAOA,IAAAA,CAAE,kBAAA,CAAmB,QAAQ;YAClCA,gMAAAA,CAAE,MAAA,CAAO;gBACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,kBAAkB;gBAClC,0MAAcA,IAAAA,CAAE,MAAA,CAAO;YACzB,CAAC;wMACDA,IAAAA,CAAE,MAAA,CAAO;gBACP,MAAMA,gMAAAA,CAAE,OAAA,CAAQ,YAAY;gBAC5B,kMAAMA,IAAAA,CAAE,MAAA,CAAO;YACjB,CAAC;wMACDA,IAAAA,CAAE,MAAA,CAAO;gBACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,gBAAgB;gBAChC,UAAUA,gMAAAA,CAAE,MAAA,CAAO;YACrB,CAAC;wMACDA,IAAAA,CAAE,MAAA,CAAO;gBACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,iBAAiB;gBACjC,uMAAWA,IAAAA,CAAE,MAAA,CAAO;YACtB,CAAC;SACF;IACH,CAAC;gMACDA,IAAAA,CAAE,MAAA,CAAO;QACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,oBAAoB;QACpC,mMAAOA,IAAAA,CAAE,MAAA,CAAO;IAClB,CAAC;IACDA,gMAAAA,CAAE,MAAA,CAAO;QACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,OAAO;QACvB,mMAAOA,IAAAA,CAAE,MAAA,CAAO;YACd,kMAAMA,IAAAA,CAAE,MAAA,CAAO;YACf,qMAASA,IAAAA,CAAE,MAAA,CAAO;QACpB,CAAC;IACH,CAAC;gMACDA,IAAAA,CAAE,MAAA,CAAO;QACP,iMAAMA,KAAAA,CAAE,OAAA,CAAQ,eAAe;QAC/B,mMAAOA,IAAAA,CAAE,MAAA,CAAO;YAAE,yMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,OAAA,CAAQ;QAAE,CAAC;QACrD,mMAAOA,IAAAA,CAAE,MAAA,CAAO;YAAE,2MAAeA,IAAAA,CAAE,MAAA,CAAO;QAAE,CAAC;IAC/C,CAAC;IACDA,gMAAAA,CAAE,MAAA,CAAO;QACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,cAAc;IAChC,CAAC;gMACDA,IAAAA,CAAE,MAAA,CAAO;QACP,kMAAMA,IAAAA,CAAE,OAAA,CAAQ,MAAM;IACxB,CAAC;CACF;AAED,IAAM,6NAAiCA,IAAAA,CAAE,MAAA,CAAO;IAC9C,sMAAUA,IAAAA,CACP,MAAA,CAAO;QACN,iMAAMA,KAAAA,CAAE,KAAA,CAAM;wMAACA,IAAAA,CAAE,OAAA,CAAQ,SAAS;wMAAGA,IAAAA,CAAE,OAAA,CAAQ,UAAU,CAAC;SAAC;QAC3D,0MAAcA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACpC,CAAC,EACA,QAAA,CAAS;AACd,CAAC;;AK9rBD,IAAM,yBAAyBC,gMAAAA,CAAE,MAAA,CAAO;IACtC,qMAASA,IAAAA,CAAE,MAAA,CAAO;IAClB,qMAASA,IAAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;AAChC,CAAC;AASD,SAAS,kBACP,UAgBI,CAAC,CAAA,EAQL;IACA,OAAO;QACL,MAAM;QACN,IAAI;QACJ,MAAM,CAAC;QACP,YAAY;QACZ,SAAS,QAAQ,OAAA;QACjB,kCAAkC,QAAQ,gCAAA;IAC5C;AACF;AAEA,IAAM,qNAAyBA,IAAAA,CAAE,MAAA,CAAO;IACtC,qMAASA,IAAAA,CAAE,MAAA,CAAO;IAClB,qMAASA,IAAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;AAChC,CAAC;AASD,SAAS,kBACP,UAgBI,CAAC,CAAA,EAQL;IACA,OAAO;QACL,MAAM;QACN,IAAI;QACJ,MAAM,CAAC;QACP,YAAY;QACZ,SAAS,QAAQ,OAAA;QACjB,kCAAkC,QAAQ,gCAAA;IAC5C;AACF;AAEA,IAAM,2NAA+BA,IAAAA,CAAE,MAAA,CAAO;IAC5C,qMAASA,IAAAA,CAAE,IAAA,CAAK;QAAC;QAAQ;QAAU;QAAe;QAAU,WAAW;KAAC;IACxE,kMAAMA,IAAAA,CAAE,MAAA,CAAO;IACf,uMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC/B,yMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,EAAE,QAAA,CAAS;IACvC,qMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC7B,qMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC7B,wMAAYA,IAAAA,CAAE,KAAA,CAAMA,gMAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;AACjD,CAAC;AASD,SAAS,wBACP,UAyCI,CAAC,CAAA,EAWL;IACA,OAAO;QACL,MAAM;QACN,IAAI;QACJ,MAAM,CAAC;QACP,YAAY;QACZ,SAAS,QAAQ,OAAA;QACjB,kCAAkC,QAAQ,gCAAA;IAC5C;AACF;AAEA,IAAM,2NAA+BA,IAAAA,CAAE,MAAA,CAAO;IAC5C,qMAASA,IAAAA,CAAE,IAAA,CAAK;QAAC;QAAQ;QAAU;QAAe;QAAU,WAAW;KAAC;IACxE,iMAAMA,KAAAA,CAAE,MAAA,CAAO;IACf,uMAAWA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC/B,yMAAaA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,EAAE,QAAA,CAAS;IACvC,oMAASA,KAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC7B,qMAASA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC7B,wMAAYA,IAAAA,CAAE,KAAA,CAAMA,gMAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;AACjD,CAAC;AASD,SAAS,wBACP,UAyCI,CAAC,CAAA,EAWL;IACA,OAAO;QACL,MAAM;QACN,IAAI;QACJ,MAAM,CAAC;QACP,YAAY;QACZ,SAAS,QAAQ,OAAA;QACjB,kCAAkC,QAAQ,gCAAA;IAC5C;AACF;AAEA,IAAM,yNAA6BA,IAAAA,CAAE,MAAA,CAAO;IAC1C,QAAQA,gMAAAA,CAAE,IAAA,CAAK;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,wMAAYA,IAAAA,CAAE,KAAA,6LAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;IAC/C,kMAAMA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAC5B,CAAC;AAYD,SAAS,sBAA8B,OAAA,EAqDrC;IACA,OAAO;QACL,MAAM;QACN,IAAI;QACJ,MAAM;YACJ,gBAAgB,QAAQ,cAAA;YACxB,iBAAiB,QAAQ,eAAA;YACzB,eAAe,QAAQ,aAAA;QACzB;QACA,YAAY;QACZ,SAAS,QAAQ,OAAA;QACjB,kCAAkC,QAAQ,gCAAA;IAC5C;AACF;AAEA,IAAM,yNAA6BA,IAAAA,CAAE,MAAA,CAAO;IAC1C,oMAAQA,IAAAA,CAAE,IAAA,CAAK;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,wMAAYA,IAAAA,CAAE,KAAA,CAAM;oMAACA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI;QAAGA,gMAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC;KAAC,EAAE,QAAA,CAAS;IACnE,sMAAUA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC9B,2MAAeA,IAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACnC,8MAAkBA,IAAAA,CAAE,IAAA,CAAK;QAAC;QAAM;QAAQ;QAAQ,OAAO;KAAC,EAAE,QAAA,CAAS;IACnE,8MAAkBA,IAAAA,CAAE,KAAA,CAAM;oMAACA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI;oMAAGA,IAAAA,CAAE,MAAA,CAAO,EAAE,GAAA,CAAI,CAAC;KAAC,EAAE,QAAA,CAAS;IACzE,iMAAMA,KAAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAC5B,CAAC;AAYD,SAAS,sBAA8B,OAAA,EAoFrC;IACA,OAAO;QACL,MAAM;QACN,IAAI;QACJ,MAAM;YACJ,gBAAgB,QAAQ,cAAA;YACxB,iBAAiB,QAAQ,eAAA;YACzB,eAAe,QAAQ,aAAA;QACzB;QACA,YAAY;QACZ,SAAS,QAAQ,OAAA;QACjB,kCAAkC,QAAQ,gCAAA;IAC5C;AACF;AAEO,IAAM,iBAAiB;IAC5B,eAAe;IACf,eAAe;IACf,qBAAqB;IACrB,qBAAqB;IACrB,mBAAmB;IACnB,mBAAmB;AACrB;;ANjbO,SAAS,gBACd,UAAqC,CAAC,CAAA,EACnB;IAxFrB,IAAA;IAyFE,MAAM,UAAA,CACJ,KAAA,CAAA,GAAA,4KAAA,CAAA,uBAAA,EAAqB,QAAQ,OAAO,CAAA,KAApC,OAAA,KAAyC;IAE3C,MAAM,aAAa,IAAA,CAAO;YACxB,qBAAqB;YACrB,8LAAa,aAAA,EAAW;gBACtB,QAAQ,QAAQ,MAAA;gBAChB,yBAAyB;gBACzB,aAAa;YACf,CAAC;YACD,GAAG,QAAQ,OAAA;QACb,CAAA;IAEA,MAAM,kBAAkB,CACtB,SACA,WAAsC,CAAC,CAAA,GAEvC,IAAI,+BAA+B,SAAS,UAAU;YACpD,UAAU;YACV;YACA,SAAS;YACT,OAAO,QAAQ,KAAA;YACf,mBAAmB;QACrB,CAAC;IAEH,MAAM,WAAW,SACf,OAAA,EACA,QAAA,EACA;QACA,IAAI,YAAY;YACd,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO,gBAAgB,SAAS,QAAQ;IAC1C;IAEA,SAAS,aAAA,GAAgB;IACzB,SAAS,IAAA,GAAO;IAChB,SAAS,QAAA,GAAW;IACpB,SAAS,kBAAA,GAAqB,CAAC,YAAoB;QACjD,MAAM,wKAAI,mBAAA,CAAiB;YAAE;YAAS,WAAW;QAAqB,CAAC;IACzE;IAEA,SAAS,KAAA,GAAQ;IAEjB,OAAO;AACT;AAKO,IAAM,YAAY,gBAAgB", "ignoreList": [0, 1, 2, 3, 4, 5, 6]}}, {"offset": {"line": 2628, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/index.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/assistant-stream-parts.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/process-chat-response.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/duplicated/usage.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/parse-partial-json.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/fix-json.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/data-stream-parts.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/process-data-stream.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/process-chat-text-response.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/process-text-stream.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/call-chat-api.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/call-completion-api.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/data-url.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/extract-max-tool-invocation-step.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/get-message-parts.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/fill-message-parts.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/is-deep-equal-data.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/prepare-attachments-for-request.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/process-assistant-stream.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/schema.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/zod-schema.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/should-resubmit-messages.ts", "turbopack:///[project]/node_modules/@ai-sdk/ui-utils/src/update-tool-call-result.ts"], "sourcesContent": ["export * from './types';\n\nexport { generateId } from '@ai-sdk/provider-utils';\n\n// Export stream data utilities for custom stream implementations,\n// both on the client and server side.\n// NOTE: this is experimental / internal and may change without notice\nexport {\n  formatAssistantStreamPart,\n  parseAssistantStreamPart,\n} from './assistant-stream-parts';\nexport type {\n  AssistantStreamPart,\n  AssistantStreamString,\n} from './assistant-stream-parts';\nexport { callChatApi } from './call-chat-api';\nexport { callCompletionApi } from './call-completion-api';\nexport { formatDataStreamPart, parseDataStreamPart } from './data-stream-parts';\nexport type { DataStreamPart, DataStreamString } from './data-stream-parts';\nexport { getTextFromDataUrl } from './data-url';\nexport type { DeepPartial } from './deep-partial';\nexport { extractMaxToolInvocationStep } from './extract-max-tool-invocation-step';\nexport { fillMessageParts } from './fill-message-parts';\nexport { getMessageParts } from './get-message-parts';\nexport { isDeepEqualData } from './is-deep-equal-data';\nexport { parsePartialJson } from './parse-partial-json';\nexport { prepareAttachmentsForRequest } from './prepare-attachments-for-request';\nexport { processAssistantStream } from './process-assistant-stream';\nexport { processDataStream } from './process-data-stream';\nexport { processTextStream } from './process-text-stream';\nexport { asSchema, jsonSchema } from './schema';\nexport type { Schema } from './schema';\nexport {\n  isAssistantMessageWithCompletedToolCalls,\n  shouldResubmitMessages,\n} from './should-resubmit-messages';\nexport { updateToolCallResult } from './update-tool-call-result';\nexport { zodSchema } from './zod-schema';\n", "import { AssistantMessage, DataMessage, JSONValue } from './types';\n\nexport type AssistantStreamString =\n  `${(typeof StreamStringPrefixes)[keyof typeof StreamStringPrefixes]}:${string}\\n`;\n\nexport interface AssistantStreamPart<\n  CODE extends string,\n  NAME extends string,\n  TYPE,\n> {\n  code: CODE;\n  name: NAME;\n  parse: (value: JSONValue) => { type: NAME; value: TYPE };\n}\n\nconst textStreamPart: AssistantStreamPart<'0', 'text', string> = {\n  code: '0',\n  name: 'text',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: 'text', value };\n  },\n};\n\nconst errorStreamPart: AssistantStreamPart<'3', 'error', string> = {\n  code: '3',\n  name: 'error',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: 'error', value };\n  },\n};\n\nconst assistantMessageStreamPart: AssistantStreamPart<\n  '4',\n  'assistant_message',\n  AssistantMessage\n> = {\n  code: '4',\n  name: 'assistant_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('id' in value) ||\n      !('role' in value) ||\n      !('content' in value) ||\n      typeof value.id !== 'string' ||\n      typeof value.role !== 'string' ||\n      value.role !== 'assistant' ||\n      !Array.isArray(value.content) ||\n      !value.content.every(\n        item =>\n          item != null &&\n          typeof item === 'object' &&\n          'type' in item &&\n          item.type === 'text' &&\n          'text' in item &&\n          item.text != null &&\n          typeof item.text === 'object' &&\n          'value' in item.text &&\n          typeof item.text.value === 'string',\n      )\n    ) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_message',\n      value: value as AssistantMessage,\n    };\n  },\n};\n\nconst assistantControlDataStreamPart: AssistantStreamPart<\n  '5',\n  'assistant_control_data',\n  {\n    threadId: string;\n    messageId: string;\n  }\n> = {\n  code: '5',\n  name: 'assistant_control_data',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('threadId' in value) ||\n      !('messageId' in value) ||\n      typeof value.threadId !== 'string' ||\n      typeof value.messageId !== 'string'\n    ) {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_control_data',\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId,\n      },\n    };\n  },\n};\n\nconst dataMessageStreamPart: AssistantStreamPart<\n  '6',\n  'data_message',\n  DataMessage\n> = {\n  code: '6',\n  name: 'data_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('role' in value) ||\n      !('data' in value) ||\n      typeof value.role !== 'string' ||\n      value.role !== 'data'\n    ) {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.',\n      );\n    }\n\n    return {\n      type: 'data_message',\n      value: value as DataMessage,\n    };\n  },\n};\n\nconst assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart,\n] as const;\n\ntype AssistantStreamParts =\n  | typeof textStreamPart\n  | typeof errorStreamPart\n  | typeof assistantMessageStreamPart\n  | typeof assistantControlDataStreamPart\n  | typeof dataMessageStreamPart;\n\ntype AssistantStreamPartValueType = {\n  [P in AssistantStreamParts as P['name']]: ReturnType<P['parse']>['value'];\n};\n\nexport type AssistantStreamPartType =\n  | ReturnType<typeof textStreamPart.parse>\n  | ReturnType<typeof errorStreamPart.parse>\n  | ReturnType<typeof assistantMessageStreamPart.parse>\n  | ReturnType<typeof assistantControlDataStreamPart.parse>\n  | ReturnType<typeof dataMessageStreamPart.parse>;\n\nexport const assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart,\n} as const;\n\nexport const StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code,\n} as const;\n\nexport const validCodes = assistantStreamParts.map(part => part.code);\n\nexport const parseAssistantStreamPart = (\n  line: string,\n): AssistantStreamPartType => {\n  const firstSeparatorIndex = line.indexOf(':');\n\n  if (firstSeparatorIndex === -1) {\n    throw new Error('Failed to parse stream string. No separator found.');\n  }\n\n  const prefix = line.slice(0, firstSeparatorIndex);\n\n  if (!validCodes.includes(prefix as keyof typeof assistantStreamPartsByCode)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n\n  const code = prefix as keyof typeof assistantStreamPartsByCode;\n\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue: JSONValue = JSON.parse(textValue);\n\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\n\nexport function formatAssistantStreamPart<\n  T extends keyof AssistantStreamPartValueType,\n>(type: T, value: AssistantStreamPartValueType[T]): AssistantStreamString {\n  const streamPart = assistantStreamParts.find(part => part.name === type);\n\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n\n  return `${streamPart.code}:${JSON.stringify(value)}\\n`;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\nimport { generateId as generateIdFunction } from '@ai-sdk/provider-utils';\nimport {\n  calculateLanguageModelUsage,\n  LanguageModelUsage,\n} from './duplicated/usage';\nimport { parsePartialJson } from './parse-partial-json';\nimport { processDataStream } from './process-data-stream';\nimport type {\n  JSONValue,\n  ReasoningUIPart,\n  TextUIPart,\n  ToolInvocation,\n  ToolInvocationUIPart,\n  UIMessage,\n  UseChatOptions,\n} from './types';\n\nexport async function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId = generateIdFunction,\n  getCurrentDate = () => new Date(),\n  lastMessage,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  update: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onToolCall?: UseChatOptions['onToolCall'];\n  onFinish?: (options: {\n    message: UIMessage | undefined;\n    finishReason: LanguageModelV1FinishReason;\n    usage: LanguageModelUsage;\n  }) => void;\n  generateId?: () => string;\n  getCurrentDate?: () => Date;\n  lastMessage: UIMessage | undefined;\n}) {\n  const replaceLastMessage = lastMessage?.role === 'assistant';\n  let step = replaceLastMessage\n    ? 1 +\n      // find max step in existing tool invocations:\n      (lastMessage.toolInvocations?.reduce((max, toolInvocation) => {\n        return Math.max(max, toolInvocation.step ?? 0);\n      }, 0) ?? 0)\n    : 0;\n\n  const message: UIMessage = replaceLastMessage\n    ? structuredClone(lastMessage)\n    : {\n        id: generateId(),\n        createdAt: getCurrentDate(),\n        role: 'assistant',\n        content: '',\n        parts: [],\n      };\n\n  let currentTextPart: TextUIPart | undefined = undefined;\n  let currentReasoningPart: ReasoningUIPart | undefined = undefined;\n  let currentReasoningTextDetail:\n    | { type: 'text'; text: string; signature?: string }\n    | undefined = undefined;\n\n  function updateToolInvocationPart(\n    toolCallId: string,\n    invocation: ToolInvocation,\n  ) {\n    const part = message.parts.find(\n      part =>\n        part.type === 'tool-invocation' &&\n        part.toolInvocation.toolCallId === toolCallId,\n    ) as ToolInvocationUIPart | undefined;\n\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: 'tool-invocation',\n        toolInvocation: invocation,\n      });\n    }\n  }\n\n  const data: JSONValue[] = [];\n\n  // keep list of current message annotations for message\n  let messageAnnotations: JSONValue[] | undefined = replaceLastMessage\n    ? lastMessage?.annotations\n    : undefined;\n\n  // keep track of partial tool calls\n  const partialToolCalls: Record<\n    string,\n    { text: string; step: number; index: number; toolName: string }\n  > = {};\n\n  let usage: LanguageModelUsage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN,\n  };\n  let finishReason: LanguageModelV1FinishReason = 'unknown';\n\n  function execUpdate() {\n    // make a copy of the data array to ensure UI is updated (SWR)\n    const copiedData = [...data];\n\n    // keeps the currentMessage up to date with the latest annotations,\n    // even if annotations preceded the message creation\n    if (messageAnnotations?.length) {\n      message.annotations = messageAnnotations;\n    }\n\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId(),\n    } as UIMessage;\n\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage,\n    });\n  }\n\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: 'text',\n          text: value,\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: 'text', text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: 'reasoning',\n          reasoning: value,\n          details: [currentReasoningTextDetail],\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n\n      message.reasoning = (message.reasoning ?? '') + value;\n\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: 'reasoning',\n          reasoning: '',\n          details: [],\n        };\n        message.parts.push(currentReasoningPart);\n      }\n\n      currentReasoningPart.details.push({\n        type: 'redacted',\n        data: value.data,\n      });\n\n      currentReasoningTextDetail = undefined;\n\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: 'file',\n        mimeType: value.mimeType,\n        data: value.data,\n      });\n\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: 'source',\n        source: value,\n      });\n\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n\n      // add the partial tool call to the map\n      partialToolCalls[value.toolCallId] = {\n        text: '',\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length,\n      };\n\n      const invocation = {\n        state: 'partial-call',\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: undefined,\n      } as const;\n\n      message.toolInvocations.push(invocation);\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n\n      partialToolCall.text += value.argsTextDelta;\n\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n\n      const invocation = {\n        state: 'partial-call',\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs,\n      } as const;\n\n      message.toolInvocations![partialToolCall.index] = invocation;\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: 'call',\n        step,\n        ...value,\n      } as const;\n\n      if (partialToolCalls[value.toolCallId] != null) {\n        // change the partial tool call to a full tool call\n        message.toolInvocations![partialToolCalls[value.toolCallId].index] =\n          invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n\n        message.toolInvocations.push(invocation);\n      }\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n\n      // invoke the onToolCall callback if it exists. This is blocking.\n      // In the future we should make this non-blocking, which\n      // requires additional state management for error handling etc.\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation = {\n            state: 'result',\n            step,\n            ...value,\n            result,\n          } as const;\n\n          // store the result in the tool invocation\n          message.toolInvocations![message.toolInvocations!.length - 1] =\n            invocation;\n\n          updateToolInvocationPart(value.toolCallId, invocation);\n\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n\n      if (toolInvocations == null) {\n        throw new Error('tool_result must be preceded by a tool_call');\n      }\n\n      // find if there is any tool invocation with the same toolCallId\n      // and replace it with the result\n      const toolInvocationIndex = toolInvocations.findIndex(\n        invocation => invocation.toolCallId === value.toolCallId,\n      );\n\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          'tool_result must be preceded by a tool_call with the same toolCallId',\n        );\n      }\n\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: 'result' as const,\n        ...value,\n      } as const;\n\n      toolInvocations[toolInvocationIndex] = invocation;\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n\n      // reset the current text and reasoning parts\n      currentTextPart = value.isContinued ? currentTextPart : undefined;\n      currentReasoningPart = undefined;\n      currentReasoningTextDetail = undefined;\n    },\n    onStartStepPart(value) {\n      // keep message id stable when we are updating an existing message:\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n\n      // add a step boundary part to the message\n      message.parts.push({ type: 'step-start' });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    },\n  });\n\n  onFinish?.({ message, finishReason, usage });\n}\n", "/**\nRepresents the number of tokens used in a prompt and completion.\n */\nexport type LanguageModelUsage = {\n  /**\nThe number of tokens used in the prompt.\n   */\n  promptTokens: number;\n\n  /**\nThe number of tokens used in the completion.\n */\n  completionTokens: number;\n\n  /**\nThe total number of tokens used (promptTokens + completionTokens).\n   */\n  totalTokens: number;\n};\n\n/**\nRepresents the number of tokens used in an embedding.\n */\nexport type EmbeddingModelUsage = {\n  /**\nThe number of tokens used in the embedding.\n   */\n  tokens: number;\n};\n\nexport function calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens,\n}: {\n  promptTokens: number;\n  completionTokens: number;\n}): LanguageModelUsage {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens,\n  };\n}\n", "import { JSONValue } from '@ai-sdk/provider';\nimport { safeParseJSON } from '@ai-sdk/provider-utils';\nimport { fixJson } from './fix-json';\n\nexport function parsePartialJson(jsonText: string | undefined): {\n  value: JSONValue | undefined;\n  state:\n    | 'undefined-input'\n    | 'successful-parse'\n    | 'repaired-parse'\n    | 'failed-parse';\n} {\n  if (jsonText === undefined) {\n    return { value: undefined, state: 'undefined-input' };\n  }\n\n  let result = safeParseJSON({ text: jsonText });\n\n  if (result.success) {\n    return { value: result.value, state: 'successful-parse' };\n  }\n\n  result = safeParseJSON({ text: fixJson(jsonText) });\n\n  if (result.success) {\n    return { value: result.value, state: 'repaired-parse' };\n  }\n\n  return { value: undefined, state: 'failed-parse' };\n}\n", "type State =\n  | 'ROOT'\n  | 'FINISH'\n  | 'INSIDE_STRING'\n  | 'INSIDE_STRING_ESCAPE'\n  | 'INSIDE_LITERAL'\n  | 'INSIDE_NUMBER'\n  | 'INSIDE_OBJECT_START'\n  | 'INSIDE_OBJECT_KEY'\n  | 'INSIDE_OBJECT_AFTER_KEY'\n  | 'INSIDE_OBJECT_BEFORE_VALUE'\n  | 'INSIDE_OBJECT_AFTER_VALUE'\n  | 'INSIDE_OBJECT_AFTER_COMMA'\n  | 'INSIDE_ARRAY_START'\n  | 'INSIDE_ARRAY_AFTER_VALUE'\n  | 'INSIDE_ARRAY_AFTER_COMMA';\n\n// Implemented as a scanner with additional fixing\n// that performs a single linear time scan pass over the partial JSON.\n//\n// The states should ideally match relevant states from the JSON spec:\n// https://www.json.org/json-en.html\n//\n// Please note that invalid JSON is not considered/covered, because it\n// is assumed that the resulting JSON will be processed by a standard\n// JSON parser that will detect any invalid JSON.\nexport function fixJson(input: string): string {\n  const stack: State[] = ['ROOT'];\n  let lastValidIndex = -1;\n  let literalStart: number | null = null;\n\n  function processValueStart(char: string, i: number, swapState: State) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_STRING');\n          break;\n        }\n\n        case 'f':\n        case 't':\n        case 'n': {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_LITERAL');\n          break;\n        }\n\n        case '-': {\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_NUMBER');\n          break;\n        }\n        case '0':\n        case '1':\n        case '2':\n        case '3':\n        case '4':\n        case '5':\n        case '6':\n        case '7':\n        case '8':\n        case '9': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_NUMBER');\n          break;\n        }\n\n        case '{': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_OBJECT_START');\n          break;\n        }\n\n        case '[': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_ARRAY_START');\n          break;\n        }\n      }\n    }\n  }\n\n  function processAfterObjectValue(char: string, i: number) {\n    switch (char) {\n      case ',': {\n        stack.pop();\n        stack.push('INSIDE_OBJECT_AFTER_COMMA');\n        break;\n      }\n      case '}': {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n\n  function processAfterArrayValue(char: string, i: number) {\n    switch (char) {\n      case ',': {\n        stack.pop();\n        stack.push('INSIDE_ARRAY_AFTER_COMMA');\n        break;\n      }\n      case ']': {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n\n    switch (currentState) {\n      case 'ROOT':\n        processValueStart(char, i, 'FINISH');\n        break;\n\n      case 'INSIDE_OBJECT_START': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_KEY');\n            break;\n          }\n          case '}': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_COMMA': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_KEY');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_KEY': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_AFTER_KEY');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_KEY': {\n        switch (char) {\n          case ':': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_BEFORE_VALUE');\n\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_BEFORE_VALUE': {\n        processValueStart(char, i, 'INSIDE_OBJECT_AFTER_VALUE');\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_VALUE': {\n        processAfterObjectValue(char, i);\n        break;\n      }\n\n      case 'INSIDE_STRING': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n\n          case '\\\\': {\n            stack.push('INSIDE_STRING_ESCAPE');\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_ARRAY_START': {\n        switch (char) {\n          case ']': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, 'INSIDE_ARRAY_AFTER_VALUE');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_ARRAY_AFTER_VALUE': {\n        switch (char) {\n          case ',': {\n            stack.pop();\n            stack.push('INSIDE_ARRAY_AFTER_COMMA');\n            break;\n          }\n\n          case ']': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_ARRAY_AFTER_COMMA': {\n        processValueStart(char, i, 'INSIDE_ARRAY_AFTER_VALUE');\n        break;\n      }\n\n      case 'INSIDE_STRING_ESCAPE': {\n        stack.pop();\n        lastValidIndex = i;\n\n        break;\n      }\n\n      case 'INSIDE_NUMBER': {\n        switch (char) {\n          case '0':\n          case '1':\n          case '2':\n          case '3':\n          case '4':\n          case '5':\n          case '6':\n          case '7':\n          case '8':\n          case '9': {\n            lastValidIndex = i;\n            break;\n          }\n\n          case 'e':\n          case 'E':\n          case '-':\n          case '.': {\n            break;\n          }\n\n          case ',': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n              processAfterArrayValue(char, i);\n            }\n\n            if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n              processAfterObjectValue(char, i);\n            }\n\n            break;\n          }\n\n          case '}': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n              processAfterObjectValue(char, i);\n            }\n\n            break;\n          }\n\n          case ']': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n              processAfterArrayValue(char, i);\n            }\n\n            break;\n          }\n\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_LITERAL': {\n        const partialLiteral = input.substring(literalStart!, i + 1);\n\n        if (\n          !'false'.startsWith(partialLiteral) &&\n          !'true'.startsWith(partialLiteral) &&\n          !'null'.startsWith(partialLiteral)\n        ) {\n          stack.pop();\n\n          if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n\n        break;\n      }\n    }\n  }\n\n  let result = input.slice(0, lastValidIndex + 1);\n\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n\n    switch (state) {\n      case 'INSIDE_STRING': {\n        result += '\"';\n        break;\n      }\n\n      case 'INSIDE_OBJECT_KEY':\n      case 'INSIDE_OBJECT_AFTER_KEY':\n      case 'INSIDE_OBJECT_AFTER_COMMA':\n      case 'INSIDE_OBJECT_START':\n      case 'INSIDE_OBJECT_BEFORE_VALUE':\n      case 'INSIDE_OBJECT_AFTER_VALUE': {\n        result += '}';\n        break;\n      }\n\n      case 'INSIDE_ARRAY_START':\n      case 'INSIDE_ARRAY_AFTER_COMMA':\n      case 'INSIDE_ARRAY_AFTER_VALUE': {\n        result += ']';\n        break;\n      }\n\n      case 'INSIDE_LITERAL': {\n        const partialLiteral = input.substring(literalStart!, input.length);\n\n        if ('true'.startsWith(partialLiteral)) {\n          result += 'true'.slice(partialLiteral.length);\n        } else if ('false'.startsWith(partialLiteral)) {\n          result += 'false'.slice(partialLiteral.length);\n        } else if ('null'.startsWith(partialLiteral)) {\n          result += 'null'.slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n\n  return result;\n}\n", "import {\n  LanguageModelV1FinishReason,\n  LanguageModelV1Source,\n} from '@ai-sdk/provider';\nimport { Tool<PERSON>all, ToolResult } from '@ai-sdk/provider-utils';\nimport { JSONValue } from './types';\n\nexport type DataStreamString =\n  `${(typeof DataStreamStringPrefixes)[keyof typeof DataStreamStringPrefixes]}:${string}\\n`;\n\nexport interface DataStreamPart<\n  CODE extends string,\n  NAME extends string,\n  TYPE,\n> {\n  code: CODE;\n  name: NAME;\n  parse: (value: JSONValue) => { type: NAME; value: TYPE };\n}\n\nconst textStreamPart: DataStreamPart<'0', 'text', string> = {\n  code: '0',\n  name: 'text',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: 'text', value };\n  },\n};\n\nconst dataStreamPart: DataStreamPart<'2', 'data', Array<JSONValue>> = {\n  code: '2',\n  name: 'data',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n\n    return { type: 'data', value };\n  },\n};\n\nconst errorStreamPart: DataStreamPart<'3', 'error', string> = {\n  code: '3',\n  name: 'error',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: 'error', value };\n  },\n};\n\nconst messageAnnotationsStreamPart: DataStreamPart<\n  '8',\n  'message_annotations',\n  Array<JSONValue>\n> = {\n  code: '8',\n  name: 'message_annotations',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n\n    return { type: 'message_annotations', value };\n  },\n};\n\nconst toolCallStreamPart: DataStreamPart<\n  '9',\n  'tool_call',\n  ToolCall<string, any>\n> = {\n  code: '9',\n  name: 'tool_call',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('toolName' in value) ||\n      typeof value.toolName !== 'string' ||\n      !('args' in value) ||\n      typeof value.args !== 'object'\n    ) {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call',\n      value: value as unknown as ToolCall<string, any>,\n    };\n  },\n};\n\nconst toolResultStreamPart: DataStreamPart<\n  'a',\n  'tool_result',\n  Omit<ToolResult<string, any, any>, 'args' | 'toolName'>\n> = {\n  code: 'a',\n  name: 'tool_result',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('result' in value)\n    ) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_result',\n      value: value as unknown as Omit<\n        ToolResult<string, any, any>,\n        'args' | 'toolName'\n      >,\n    };\n  },\n};\n\nconst toolCallStreamingStartStreamPart: DataStreamPart<\n  'b',\n  'tool_call_streaming_start',\n  { toolCallId: string; toolName: string }\n> = {\n  code: 'b',\n  name: 'tool_call_streaming_start',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('toolName' in value) ||\n      typeof value.toolName !== 'string'\n    ) {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call_streaming_start',\n      value: value as unknown as { toolCallId: string; toolName: string },\n    };\n  },\n};\n\nconst toolCallDeltaStreamPart: DataStreamPart<\n  'c',\n  'tool_call_delta',\n  { toolCallId: string; argsTextDelta: string }\n> = {\n  code: 'c',\n  name: 'tool_call_delta',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('argsTextDelta' in value) ||\n      typeof value.argsTextDelta !== 'string'\n    ) {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call_delta',\n      value: value as unknown as {\n        toolCallId: string;\n        argsTextDelta: string;\n      },\n    };\n  },\n};\n\nconst finishMessageStreamPart: DataStreamPart<\n  'd',\n  'finish_message',\n  {\n    finishReason: LanguageModelV1FinishReason;\n    // TODO v5 remove usage from finish event (only on step-finish)\n    usage?: {\n      promptTokens: number;\n      completionTokens: number;\n    };\n  }\n> = {\n  code: 'd',\n  name: 'finish_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('finishReason' in value) ||\n      typeof value.finishReason !== 'string'\n    ) {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.',\n      );\n    }\n\n    const result: {\n      finishReason: LanguageModelV1FinishReason;\n      usage?: {\n        promptTokens: number;\n        completionTokens: number;\n      };\n    } = {\n      finishReason: value.finishReason as LanguageModelV1FinishReason,\n    };\n\n    if (\n      'usage' in value &&\n      value.usage != null &&\n      typeof value.usage === 'object' &&\n      'promptTokens' in value.usage &&\n      'completionTokens' in value.usage\n    ) {\n      result.usage = {\n        promptTokens:\n          typeof value.usage.promptTokens === 'number'\n            ? value.usage.promptTokens\n            : Number.NaN,\n        completionTokens:\n          typeof value.usage.completionTokens === 'number'\n            ? value.usage.completionTokens\n            : Number.NaN,\n      };\n    }\n\n    return {\n      type: 'finish_message',\n      value: result,\n    };\n  },\n};\n\nconst finishStepStreamPart: DataStreamPart<\n  'e',\n  'finish_step',\n  {\n    isContinued: boolean;\n    finishReason: LanguageModelV1FinishReason;\n    usage?: {\n      promptTokens: number;\n      completionTokens: number;\n    };\n  }\n> = {\n  code: 'e',\n  name: 'finish_step',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('finishReason' in value) ||\n      typeof value.finishReason !== 'string'\n    ) {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.',\n      );\n    }\n\n    const result: {\n      isContinued: boolean;\n      finishReason: LanguageModelV1FinishReason;\n      usage?: {\n        promptTokens: number;\n        completionTokens: number;\n      };\n    } = {\n      finishReason: value.finishReason as LanguageModelV1FinishReason,\n      isContinued: false,\n    };\n\n    if (\n      'usage' in value &&\n      value.usage != null &&\n      typeof value.usage === 'object' &&\n      'promptTokens' in value.usage &&\n      'completionTokens' in value.usage\n    ) {\n      result.usage = {\n        promptTokens:\n          typeof value.usage.promptTokens === 'number'\n            ? value.usage.promptTokens\n            : Number.NaN,\n        completionTokens:\n          typeof value.usage.completionTokens === 'number'\n            ? value.usage.completionTokens\n            : Number.NaN,\n      };\n    }\n\n    if ('isContinued' in value && typeof value.isContinued === 'boolean') {\n      result.isContinued = value.isContinued;\n    }\n\n    return {\n      type: 'finish_step',\n      value: result,\n    };\n  },\n};\n\nconst startStepStreamPart: DataStreamPart<\n  'f',\n  'start_step',\n  {\n    messageId: string;\n  }\n> = {\n  code: 'f',\n  name: 'start_step',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('messageId' in value) ||\n      typeof value.messageId !== 'string'\n    ) {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.',\n      );\n    }\n\n    return {\n      type: 'start_step',\n      value: {\n        messageId: value.messageId,\n      },\n    };\n  },\n};\n\nconst reasoningStreamPart: DataStreamPart<'g', 'reasoning', string> = {\n  code: 'g',\n  name: 'reasoning',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: 'reasoning', value };\n  },\n};\n\nconst sourcePart: DataStreamPart<'h', 'source', LanguageModelV1Source> = {\n  code: 'h',\n  name: 'source',\n  parse: (value: JSONValue) => {\n    if (value == null || typeof value !== 'object') {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n\n    return {\n      type: 'source',\n      value: value as LanguageModelV1Source,\n    };\n  },\n};\n\nconst redactedReasoningStreamPart: DataStreamPart<\n  'i',\n  'redacted_reasoning',\n  { data: string }\n> = {\n  code: 'i',\n  name: 'redacted_reasoning',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('data' in value) ||\n      typeof value.data !== 'string'\n    ) {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.',\n      );\n    }\n    return { type: 'redacted_reasoning', value: { data: value.data } };\n  },\n};\n\nconst reasoningSignatureStreamPart: DataStreamPart<\n  'j',\n  'reasoning_signature',\n  { signature: string }\n> = {\n  code: 'j',\n  name: 'reasoning_signature',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('signature' in value) ||\n      typeof value.signature !== 'string'\n    ) {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.',\n      );\n    }\n    return {\n      type: 'reasoning_signature',\n      value: { signature: value.signature },\n    };\n  },\n};\n\nconst fileStreamPart: DataStreamPart<\n  'k',\n  'file',\n  {\n    data: string; // base64 encoded data\n    mimeType: string;\n  }\n> = {\n  code: 'k',\n  name: 'file',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('data' in value) ||\n      typeof value.data !== 'string' ||\n      !('mimeType' in value) ||\n      typeof value.mimeType !== 'string'\n    ) {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.',\n      );\n    }\n    return { type: 'file', value: value as { data: string; mimeType: string } };\n  },\n};\n\nconst dataStreamParts = [\n  textStreamPart,\n  dataStreamPart,\n  errorStreamPart,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart,\n] as const;\n\nexport const dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map(part => [part.code, part]),\n) as {\n  [K in (typeof dataStreamParts)[number]['code']]: (typeof dataStreamParts)[number];\n};\n\ntype DataStreamParts = (typeof dataStreamParts)[number];\n\n/**\n * Maps the type of a stream part to its value type.\n */\ntype DataStreamPartValueType = {\n  [P in DataStreamParts as P['name']]: ReturnType<P['parse']>['value'];\n};\n\nexport type DataStreamPartType = ReturnType<DataStreamParts['parse']>;\n\n/**\n * The map of prefixes for data in the stream\n *\n * - 0: Text from the LLM response\n * - 1: (OpenAI) function_call responses\n * - 2: custom JSON added by the user using `Data`\n * - 6: (OpenAI) tool_call responses\n *\n * Example:\n * ```\n * 0:Vercel\n * 0:'s\n * 0: AI\n * 0: AI\n * 0: SDK\n * 0: is great\n * 0:!\n * 2: { \"someJson\": \"value\" }\n * 1: {\"function_call\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}\n * 6: {\"tool_call\": {\"id\": \"tool_0\", \"type\": \"function\", \"function\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}}\n *```\n */\nexport const DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map(part => [part.name, part.code]),\n) as {\n  [K in DataStreamParts['name']]: (typeof dataStreamParts)[number]['code'];\n};\n\nexport const validCodes = dataStreamParts.map(part => part.code);\n\n/**\nParses a stream part from a string.\n\n@param line The string to parse.\n@returns The parsed stream part.\n@throws An error if the string cannot be parsed.\n */\nexport const parseDataStreamPart = (line: string): DataStreamPartType => {\n  const firstSeparatorIndex = line.indexOf(':');\n\n  if (firstSeparatorIndex === -1) {\n    throw new Error('Failed to parse stream string. No separator found.');\n  }\n\n  const prefix = line.slice(0, firstSeparatorIndex);\n\n  if (!validCodes.includes(prefix as keyof typeof dataStreamPartsByCode)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n\n  const code = prefix as keyof typeof dataStreamPartsByCode;\n\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue: JSONValue = JSON.parse(textValue);\n\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\n\n/**\nPrepends a string with a prefix from the `StreamChunkPrefixes`, JSON-ifies it,\nand appends a new line.\n\nIt ensures type-safety for the part type and value.\n */\nexport function formatDataStreamPart<T extends keyof DataStreamPartValueType>(\n  type: T,\n  value: DataStreamPartValueType[T],\n): DataStreamString {\n  const streamPart = dataStreamParts.find(part => part.name === type);\n\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n\n  return `${streamPart.code}:${JSON.stringify(value)}\\n`;\n}\n", "import { DataStreamPartType, parseDataStreamPart } from './data-stream-parts';\n\nconst NEWLINE = '\\n'.charCodeAt(0);\n\n// concatenates all the chunks into a single Uint8Array\nfunction concatChunks(chunks: Uint8Array[], totalLength: number) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n\n  return concatenatedChunks;\n}\n\nexport async function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart?: (\n    streamPart: (DataStreamPartType & { type: 'text' })['value'],\n  ) => Promise<void> | void;\n  onReasoningPart?: (\n    streamPart: (DataStreamPartType & { type: 'reasoning' })['value'],\n  ) => Promise<void> | void;\n  onReasoningSignaturePart?: (\n    streamPart: (DataStreamPartType & { type: 'reasoning_signature' })['value'],\n  ) => Promise<void> | void;\n  onRedactedReasoningPart?: (\n    streamPart: (DataStreamPartType & { type: 'redacted_reasoning' })['value'],\n  ) => Promise<void> | void;\n  onFilePart?: (\n    streamPart: (DataStreamPartType & { type: 'file' })['value'],\n  ) => Promise<void> | void;\n  onSourcePart?: (\n    streamPart: (DataStreamPartType & { type: 'source' })['value'],\n  ) => Promise<void> | void;\n  onDataPart?: (\n    streamPart: (DataStreamPartType & { type: 'data' })['value'],\n  ) => Promise<void> | void;\n  onErrorPart?: (\n    streamPart: (DataStreamPartType & { type: 'error' })['value'],\n  ) => Promise<void> | void;\n  onToolCallStreamingStartPart?: (\n    streamPart: (DataStreamPartType & {\n      type: 'tool_call_streaming_start';\n    })['value'],\n  ) => Promise<void> | void;\n  onToolCallDeltaPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_call_delta' })['value'],\n  ) => Promise<void> | void;\n  onToolCallPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_call' })['value'],\n  ) => Promise<void> | void;\n  onToolResultPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_result' })['value'],\n  ) => Promise<void> | void;\n  onMessageAnnotationsPart?: (\n    streamPart: (DataStreamPartType & {\n      type: 'message_annotations';\n    })['value'],\n  ) => Promise<void> | void;\n  onFinishMessagePart?: (\n    streamPart: (DataStreamPartType & { type: 'finish_message' })['value'],\n  ) => Promise<void> | void;\n  onFinishStepPart?: (\n    streamPart: (DataStreamPartType & { type: 'finish_step' })['value'],\n  ) => Promise<void> | void;\n  onStartStepPart?: (\n    streamPart: (DataStreamPartType & { type: 'start_step' })['value'],\n  ) => Promise<void> | void;\n}): Promise<void> {\n  // implementation note: this slightly more complex algorithm is required\n  // to pass the tests in the edge environment.\n\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks: Uint8Array[] = [];\n  let totalLength = 0;\n\n  while (true) {\n    const { value } = await reader.read();\n\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        // if the last character is not a newline, we have not read the whole JSON value\n        continue;\n      }\n    }\n\n    if (chunks.length === 0) {\n      break; // we have reached the end of the stream\n    }\n\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n\n    const streamParts = decoder\n      .decode(concatenatedChunks, { stream: true })\n      .split('\\n')\n      .filter(line => line !== '') // splitting leaves an empty string at the end\n      .map(parseDataStreamPart);\n\n    for (const { type, value } of streamParts) {\n      switch (type) {\n        case 'text':\n          await onTextPart?.(value);\n          break;\n        case 'reasoning':\n          await onReasoningPart?.(value);\n          break;\n        case 'reasoning_signature':\n          await onReasoningSignaturePart?.(value);\n          break;\n        case 'redacted_reasoning':\n          await onRedactedReasoningPart?.(value);\n          break;\n        case 'file':\n          await onFilePart?.(value);\n          break;\n        case 'source':\n          await onSourcePart?.(value);\n          break;\n        case 'data':\n          await onDataPart?.(value);\n          break;\n        case 'error':\n          await onErrorPart?.(value);\n          break;\n        case 'message_annotations':\n          await onMessageAnnotationsPart?.(value);\n          break;\n        case 'tool_call_streaming_start':\n          await onToolCallStreamingStartPart?.(value);\n          break;\n        case 'tool_call_delta':\n          await onToolCallDeltaPart?.(value);\n          break;\n        case 'tool_call':\n          await onToolCallPart?.(value);\n          break;\n        case 'tool_result':\n          await onToolResultPart?.(value);\n          break;\n        case 'finish_message':\n          await onFinishMessagePart?.(value);\n          break;\n        case 'finish_step':\n          await onFinishStepPart?.(value);\n          break;\n        case 'start_step':\n          await onStartStepPart?.(value);\n          break;\n        default: {\n          const exhaustiveCheck: never = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n", "import { JSONValue } from '@ai-sdk/provider';\nimport { generateId as generateIdFunction } from '@ai-sdk/provider-utils';\nimport { processTextStream } from './process-text-stream';\nimport { TextUIPart, UIMessage, UseChatOptions } from './types';\n\nexport async function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => new Date(),\n  generateId = generateIdFunction,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  update: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onFinish: UseChatOptions['onFinish'];\n  getCurrentDate?: () => Date;\n  generateId?: () => string;\n}) {\n  const textPart: TextUIPart = { type: 'text', text: '' };\n\n  const resultMessage: UIMessage = {\n    id: generateId(),\n    createdAt: getCurrentDate(),\n    role: 'assistant' as const,\n    content: '',\n    parts: [textPart],\n  };\n\n  await processTextStream({\n    stream,\n    onTextPart: chunk => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n\n      // note: creating a new message object is required for Solid.js streaming\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false,\n      });\n    },\n  });\n\n  // in text mode, we don't have usage information or finish reason:\n  onFinish?.(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: 'unknown',\n  });\n}\n", "export async function processTextStream({\n  stream,\n  onTextPart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart: (chunk: string) => Promise<void> | void;\n}): Promise<void> {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n", "import { processChatResponse } from './process-chat-response';\nimport { processChatTextResponse } from './process-chat-text-response';\nimport { IdGenerator, JSONValue, UIMessage, UseChatOptions } from './types';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport async function callChatApi({\n  api,\n  body,\n  streamProtocol = 'data',\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId,\n  fetch = getOriginalFetch(),\n  lastMessage,\n  requestType = 'generate',\n}: {\n  api: string;\n  body: Record<string, any>;\n  streamProtocol: 'data' | 'text' | undefined;\n  credentials: RequestCredentials | undefined;\n  headers: HeadersInit | undefined;\n  abortController: (() => AbortController | null) | undefined;\n  restoreMessagesOnFailure: () => void;\n  onResponse: ((response: Response) => void | Promise<void>) | undefined;\n  onUpdate: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onFinish: UseChatOptions['onFinish'];\n  onToolCall: UseChatOptions['onToolCall'];\n  generateId: IdGenerator;\n  fetch: ReturnType<typeof getOriginalFetch> | undefined;\n  lastMessage: UIMessage | undefined;\n  requestType?: 'generate' | 'resume';\n}) {\n  const request =\n    requestType === 'resume'\n      ? fetch(`${api}?chatId=${body.id}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            ...headers,\n          },\n          signal: abortController?.()?.signal,\n          credentials,\n        })\n      : fetch(api, {\n          method: 'POST',\n          body: JSON.stringify(body),\n          headers: {\n            'Content-Type': 'application/json',\n            ...headers,\n          },\n          signal: abortController?.()?.signal,\n          credentials,\n        });\n\n  const response = await request.catch(err => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (await response.text()) ?? 'Failed to fetch the chat response.',\n    );\n  }\n\n  if (!response.body) {\n    throw new Error('The response body is empty.');\n  }\n\n  switch (streamProtocol) {\n    case 'text': {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId,\n      });\n      return;\n    }\n\n    case 'data': {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId,\n      });\n      return;\n    }\n\n    default: {\n      const exhaustiveCheck: never = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n", "import { processTextStream } from './process-text-stream';\nimport { processDataStream } from './process-data-stream';\nimport { JSONValue } from './types';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport async function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = 'data',\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch = getOriginalFetch(),\n}: {\n  api: string;\n  prompt: string;\n  credentials: RequestCredentials | undefined;\n  headers: HeadersInit | undefined;\n  body: Record<string, any>;\n  streamProtocol: 'data' | 'text' | undefined;\n  setCompletion: (completion: string) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: Error | undefined) => void;\n  setAbortController: (abortController: AbortController | null) => void;\n  onResponse: ((response: Response) => void | Promise<void>) | undefined;\n  onFinish: ((prompt: string, completion: string) => void) | undefined;\n  onError: ((error: Error) => void) | undefined;\n  onData: ((data: JSONValue[]) => void) | undefined;\n  fetch: ReturnType<typeof getOriginalFetch> | undefined;\n}) {\n  try {\n    setLoading(true);\n    setError(undefined);\n\n    const abortController = new AbortController();\n    setAbortController(abortController);\n\n    // Empty the completion immediately.\n    setCompletion('');\n\n    const response = await fetch(api, {\n      method: 'POST',\n      body: JSON.stringify({\n        prompt,\n        ...body,\n      }),\n      credentials,\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      signal: abortController.signal,\n    }).catch(err => {\n      throw err;\n    });\n\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n\n    if (!response.ok) {\n      throw new Error(\n        (await response.text()) ?? 'Failed to fetch the chat response.',\n      );\n    }\n\n    if (!response.body) {\n      throw new Error('The response body is empty.');\n    }\n\n    let result = '';\n\n    switch (streamProtocol) {\n      case 'text': {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: chunk => {\n            result += chunk;\n            setCompletion(result);\n          },\n        });\n        break;\n      }\n      case 'data': {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData?.(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          },\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck: never = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    // Ignore abort errors as they are expected.\n    if ((err as any).name === 'AbortError') {\n      setAbortController(null);\n      return null;\n    }\n\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n\n    setError(err as Error);\n  } finally {\n    setLoading(false);\n  }\n}\n", "/**\n * Converts a data URL of type text/* to a text string.\n */\nexport function getTextFromDataUrl(dataUrl: string): string {\n  const [header, base64Content] = dataUrl.split(',');\n  const mimeType = header.split(';')[0].split(':')[1];\n\n  if (mimeType == null || base64Content == null) {\n    throw new Error('Invalid data URL format');\n  }\n\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n", "import { ToolInvocation } from './types';\n\nexport function extractMaxToolInvocationStep(\n  toolInvocations: ToolInvocation[] | undefined,\n): number | undefined {\n  return toolInvocations?.reduce((max, toolInvocation) => {\n    return Math.max(max, toolInvocation.step ?? 0);\n  }, 0);\n}\n", "import {\n  CreateMessage,\n  FileUIPart,\n  Message,\n  ReasoningUIPart,\n  SourceUIPart,\n  StepStartUIPart,\n  TextUIPart,\n  ToolInvocationUIPart,\n  UIMessage,\n} from './types';\n\nexport function getMessageParts(\n  message: Message | CreateMessage | UIMessage,\n): (\n  | TextUIPart\n  | ReasoningUIPart\n  | ToolInvocationUIPart\n  | SourceUIPart\n  | FileUIPart\n  | StepStartUIPart\n)[] {\n  return (\n    message.parts ?? [\n      ...(message.toolInvocations\n        ? message.toolInvocations.map(toolInvocation => ({\n            type: 'tool-invocation' as const,\n            toolInvocation,\n          }))\n        : []),\n      ...(message.reasoning\n        ? [\n            {\n              type: 'reasoning' as const,\n              reasoning: message.reasoning,\n              details: [{ type: 'text' as const, text: message.reasoning }],\n            },\n          ]\n        : []),\n      ...(message.content\n        ? [{ type: 'text' as const, text: message.content }]\n        : []),\n    ]\n  );\n}\n", "import { getMessageParts } from './get-message-parts';\nimport { Message, UIMessage } from './types';\n\nexport function fillMessageParts(messages: Message[]): UIMessage[] {\n  return messages.map(message => ({\n    ...message,\n    parts: getMessageParts(message),\n  }));\n}\n", "/**\n * Performs a deep-equal comparison of two parsed JSON objects.\n *\n * @param {any} obj1 - The first object to compare.\n * @param {any} obj2 - The second object to compare.\n * @returns {boolean} - Returns true if the two objects are deeply equal, false otherwise.\n */\nexport function isDeepEqualData(obj1: any, obj2: any): boolean {\n  // Check for strict equality first\n  if (obj1 === obj2) return true;\n\n  // Check if either is null or undefined\n  if (obj1 == null || obj2 == null) return false;\n\n  // Check if both are objects\n  if (typeof obj1 !== 'object' && typeof obj2 !== 'object')\n    return obj1 === obj2;\n\n  // If they are not strictly equal, they both need to be Objects\n  if (obj1.constructor !== obj2.constructor) return false;\n\n  // Special handling for Date objects\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n\n  // Handle arrays: compare length and then perform a recursive deep comparison on each item\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length) return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i])) return false;\n    }\n    return true; // All array elements matched\n  }\n\n  // Compare the set of keys in each object\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length) return false;\n\n  // Check each key-value pair recursively\n  for (const key of keys1) {\n    if (!keys2.includes(key)) return false;\n    if (!isDeepEqualData(obj1[key], obj2[key])) return false;\n  }\n\n  return true; // All keys and values matched\n}\n", "import { Attachment } from './types';\n\nexport async function prepareAttachmentsForRequest(\n  attachmentsFromOptions: FileList | Array<Attachment> | undefined,\n) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n\n  // https://github.com/vercel/ai/pull/6045\n  // React-native doesn't have a FileList\n  // global variable, so we need to check for it\n  if (\n    globalThis.FileList &&\n    attachmentsFromOptions instanceof globalThis.FileList\n  ) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async attachment => {\n        const { name, type } = attachment;\n\n        const dataUrl = await new Promise<string>((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = readerEvent => {\n            resolve(readerEvent.target?.result as string);\n          };\n          reader.onerror = error => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n\n        return {\n          name,\n          contentType: type,\n          url: dataUrl,\n        };\n      }),\n    );\n  }\n\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n\n  throw new Error('Invalid attachments type');\n}\n", "import {\n  AssistantStreamPartType,\n  parseAssistantStreamPart,\n} from './assistant-stream-parts';\n\nconst NEWLINE = '\\n'.charCodeAt(0);\n\n// concatenates all the chunks into a single Uint8Array\nfunction concatChunks(chunks: Uint8Array[], totalLength: number) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n\n  return concatenatedChunks;\n}\n\nexport async function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart?: (\n    streamPart: (AssistantStreamPartType & { type: 'text' })['value'],\n  ) => Promise<void> | void;\n  onErrorPart?: (\n    streamPart: (AssistantStreamPartType & { type: 'error' })['value'],\n  ) => Promise<void> | void;\n  onAssistantMessagePart?: (\n    streamPart: (AssistantStreamPartType & {\n      type: 'assistant_message';\n    })['value'],\n  ) => Promise<void> | void;\n  onAssistantControlDataPart?: (\n    streamPart: (AssistantStreamPartType & {\n      type: 'assistant_control_data';\n    })['value'],\n  ) => Promise<void> | void;\n  onDataMessagePart?: (\n    streamPart: (AssistantStreamPartType & { type: 'data_message' })['value'],\n  ) => Promise<void> | void;\n}): Promise<void> {\n  // implementation note: this slightly more complex algorithm is required\n  // to pass the tests in the edge environment.\n\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks: Uint8Array[] = [];\n  let totalLength = 0;\n\n  while (true) {\n    const { value } = await reader.read();\n\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        // if the last character is not a newline, we have not read the whole JSON value\n        continue;\n      }\n    }\n\n    if (chunks.length === 0) {\n      break; // we have reached the end of the stream\n    }\n\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n\n    const streamParts = decoder\n      .decode(concatenatedChunks, { stream: true })\n      .split('\\n')\n      .filter(line => line !== '')\n      .map(parseAssistantStreamPart);\n\n    for (const { type, value } of streamParts) {\n      switch (type) {\n        case 'text':\n          await onTextPart?.(value);\n          break;\n        case 'error':\n          await onErrorPart?.(value);\n          break;\n        case 'assistant_message':\n          await onAssistantMessagePart?.(value);\n          break;\n        case 'assistant_control_data':\n          await onAssistantControlDataPart?.(value);\n          break;\n        case 'data_message':\n          await onDataMessagePart?.(value);\n          break;\n        default: {\n          const exhaustiveCheck: never = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n", "import { Validator, validatorSymbol } from '@ai-sdk/provider-utils';\nimport { JSONSchema7 } from 'json-schema';\nimport { z } from 'zod';\nimport { zodSchema } from './zod-schema';\n\n/**\n * Used to mark schemas so we can support both Zod and custom schemas.\n */\nconst schemaSymbol = Symbol.for('vercel.ai.schema');\n\nexport type Schema<OBJECT = unknown> = Validator<OBJECT> & {\n  /**\n   * Used to mark schemas so we can support both Zod and custom schemas.\n   */\n  [schemaSymbol]: true;\n\n  /**\n   * Schema type for inference.\n   */\n  _type: OBJECT;\n\n  /**\n   * The JSON Schema for the schema. It is passed to the providers.\n   */\n  readonly jsonSchema: JSONSchema7;\n};\n\n/**\n * Create a schema using a JSON Schema.\n *\n * @param jsonSchema The JSON Schema for the schema.\n * @param options.validate Optional. A validation function for the schema.\n */\nexport function jsonSchema<OBJECT = unknown>(\n  jsonSchema: JSONSchema7,\n  {\n    validate,\n  }: {\n    validate?: (\n      value: unknown,\n    ) => { success: true; value: OBJECT } | { success: false; error: Error };\n  } = {},\n): Schema<OBJECT> {\n  return {\n    [schemaSymbol]: true,\n    _type: undefined as OBJECT, // should never be used directly\n    [validatorSymbol]: true,\n    jsonSchema,\n    validate,\n  };\n}\n\nfunction isSchema(value: unknown): value is Schema {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    schemaSymbol in value &&\n    value[schemaSymbol] === true &&\n    'jsonSchema' in value &&\n    'validate' in value\n  );\n}\n\nexport function asSchema<OBJECT>(\n  schema: z.Schema<OBJECT, z.ZodTypeDef, any> | Schema<OBJECT>,\n): Schema<OBJECT> {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n", "import { JSONSchema7 } from 'json-schema';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { jsonSchema, Schema } from './schema';\n\nexport function zodSchema<OBJECT>(\n  zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  return jsonSchema(\n    zodToJsonSchema(zodSchema, {\n      $refStrategy: useReferences ? 'root' : 'none',\n      target: 'jsonSchema7', // note: openai mode breaks various gemini conversions\n    }) as JSONSchema7,\n    {\n      validate: value => {\n        const result = zodSchema.safeParse(value);\n        return result.success\n          ? { success: true, value: result.data }\n          : { success: false, error: result.error };\n      },\n    },\n  );\n}\n", "import { extractMaxToolInvocationStep } from './extract-max-tool-invocation-step';\nimport { UIMessage } from './types';\n\nexport function shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages,\n}: {\n  originalMaxToolInvocationStep: number | undefined;\n  originalMessageCount: number;\n  maxSteps: number;\n  messages: UIMessage[];\n}) {\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 &&\n    // ensure there is a last message:\n    lastMessage != null &&\n    // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount ||\n      extractMaxToolInvocationStep(lastMessage.toolInvocations) !==\n        originalMaxToolInvocationStep) &&\n    // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) &&\n    // limit the number of automatic steps:\n    (extractMaxToolInvocationStep(lastMessage.toolInvocations) ?? 0) < maxSteps\n  );\n}\n\n/**\nCheck if the message is an assistant message with completed tool calls.\nThe last step of the message must have at least one tool invocation and\nall tool invocations must have a result.\n */\nexport function isAssistantMessageWithCompletedToolCalls(\n  message: UIMessage,\n): message is UIMessage & {\n  role: 'assistant';\n} {\n  if (message.role !== 'assistant') {\n    return false;\n  }\n\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === 'step-start' ? index : lastIndex;\n  }, -1);\n\n  const lastStepToolInvocations = message.parts\n    .slice(lastStepStartIndex + 1)\n    .filter(part => part.type === 'tool-invocation');\n\n  return (\n    lastStepToolInvocations.length > 0 &&\n    lastStepToolInvocations.every(part => 'result' in part.toolInvocation)\n  );\n}\n", "import { ToolInvocationUIPart, UIMessage } from './types';\n\n/**\n * Updates the result of a specific tool invocation in the last message of the given messages array.\n *\n * @param {object} params - The parameters object.\n * @param {UIMessage[]} params.messages - An array of messages, from which the last one is updated.\n * @param {string} params.toolCallId - The unique identifier for the tool invocation to update.\n * @param {unknown} params.toolResult - The result object to attach to the tool invocation.\n * @returns {void} This function does not return anything.\n */\nexport function updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result,\n}: {\n  messages: UIMessage[];\n  toolCallId: string;\n  toolResult: unknown;\n}) {\n  const lastMessage = messages[messages.length - 1];\n\n  const invocationPart = lastMessage.parts.find(\n    (part): part is ToolInvocationUIPart =>\n      part.type === 'tool-invocation' &&\n      part.toolInvocation.toolCallId === toolCallId,\n  );\n\n  if (invocationPart == null) {\n    return;\n  }\n\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: 'result' as const,\n    result,\n  };\n\n  invocationPart.toolInvocation = toolResult;\n\n  lastMessage.toolInvocations = lastMessage.toolInvocations?.map(\n    toolInvocation =>\n      toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation,\n  );\n}\n"], "names": ["textStreamPart", "errorStreamPart", "validCodes", "value", "generateId", "_a", "part", "invocation", "generateIdFunction", "generateId", "generateIdFunction", "generateId", "fetch", "getOriginalFetch", "fetch", "NEWLINE", "concatChunks", "value", "zodSchema", "jsonSchema"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AECA,SAAS,cAAc,0BAA0B;;AkBCjD,OAAO,qBAAqB;;;;AnBa5B,IAAM,iBAA2D;IAC/D,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,qCAAqC;QACvD;QACA,OAAO;YAAE,MAAM;YAAQ;QAAM;IAC/B;AACF;AAEA,IAAM,kBAA6D;IACjE,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,sCAAsC;QACxD;QACA,OAAO;YAAE,MAAM;YAAS;QAAM;IAChC;AACF;AAEA,IAAM,6BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,QAAQ,KAAA,KACV,CAAA,CAAE,UAAU,KAAA,KACZ,CAAA,CAAE,aAAa,KAAA,KACf,OAAO,MAAM,EAAA,KAAO,YACpB,OAAO,MAAM,IAAA,KAAS,YACtB,MAAM,IAAA,KAAS,eACf,CAAC,MAAM,OAAA,CAAQ,MAAM,OAAO,KAC5B,CAAC,MAAM,OAAA,CAAQ,KAAA,CACb,CAAA,OACE,QAAQ,QACR,OAAO,SAAS,YAChB,UAAU,QACV,KAAK,IAAA,KAAS,UACd,UAAU,QACV,KAAK,IAAA,IAAQ,QACb,OAAO,KAAK,IAAA,KAAS,YACrB,WAAW,KAAK,IAAA,IAChB,OAAO,KAAK,IAAA,CAAK,KAAA,KAAU,WAE/B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,iCAOF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,cAAc,KAAA,KAChB,CAAA,CAAE,eAAe,KAAA,KACjB,OAAO,MAAM,QAAA,KAAa,YAC1B,OAAO,MAAM,SAAA,KAAc,UAC3B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN,OAAO;gBACL,UAAU,MAAM,QAAA;gBAChB,WAAW,MAAM,SAAA;YACnB;QACF;IACF;AACF;AAEA,IAAM,wBAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,UAAU,KAAA,KACZ,CAAA,CAAE,UAAU,KAAA,KACZ,OAAO,MAAM,IAAA,KAAS,YACtB,MAAM,IAAA,KAAS,QACf;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACF;AAoBO,IAAM,6BAA6B;IACxC,CAAC,eAAe,IAAI,CAAA,EAAG;IACvB,CAAC,gBAAgB,IAAI,CAAA,EAAG;IACxB,CAAC,2BAA2B,IAAI,CAAA,EAAG;IACnC,CAAC,+BAA+B,IAAI,CAAA,EAAG;IACvC,CAAC,sBAAsB,IAAI,CAAA,EAAG;AAChC;AAEO,IAAM,uBAAuB;IAClC,CAAC,eAAe,IAAI,CAAA,EAAG,eAAe,IAAA;IACtC,CAAC,gBAAgB,IAAI,CAAA,EAAG,gBAAgB,IAAA;IACxC,CAAC,2BAA2B,IAAI,CAAA,EAAG,2BAA2B,IAAA;IAC9D,CAAC,+BAA+B,IAAI,CAAA,EAAG,+BAA+B,IAAA;IACtE,CAAC,sBAAsB,IAAI,CAAA,EAAG,sBAAsB,IAAA;AACtD;AAEO,IAAM,aAAa,qBAAqB,GAAA,CAAI,CAAA,OAAQ,KAAK,IAAI;AAE7D,IAAM,2BAA2B,CACtC,SAC4B;IAC5B,MAAM,sBAAsB,KAAK,OAAA,CAAQ,GAAG;IAE5C,IAAI,wBAAwB,CAAA,GAAI;QAC9B,MAAM,IAAI,MAAM,oDAAoD;IACtE;IAEA,MAAM,SAAS,KAAK,KAAA,CAAM,GAAG,mBAAmB;IAEhD,IAAI,CAAC,WAAW,QAAA,CAAS,MAAiD,GAAG;QAC3E,MAAM,IAAI,MAAM,CAAA,4CAAA,EAA+C,MAAM,CAAA,CAAA,CAAG;IAC1E;IAEA,MAAM,OAAO;IAEb,MAAM,YAAY,KAAK,KAAA,CAAM,sBAAsB,CAAC;IACpD,MAAM,YAAuB,KAAK,KAAA,CAAM,SAAS;IAEjD,OAAO,0BAAA,CAA2B,IAAI,CAAA,CAAE,KAAA,CAAM,SAAS;AACzD;AAEO,SAAS,0BAEd,IAAA,EAAS,KAAA,EAA+D;IACxE,MAAM,aAAa,qBAAqB,IAAA,CAAK,CAAA,OAAQ,KAAK,IAAA,KAAS,IAAI;IAEvE,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,IAAI,EAAE;IACrD;IAEA,OAAO,GAAG,WAAW,IAAI,CAAA,CAAA,EAAI,KAAK,SAAA,CAAU,KAAK,CAAC,CAAA;AAAA,CAAA;AACpD;;;AE7LO,SAAS,4BAA4B,EAC1C,YAAA,EACA,gBAAA,EACF,EAGuB;IACrB,OAAO;QACL;QACA;QACA,aAAa,eAAe;IAC9B;AACF;;;AEhBO,SAAS,QAAQ,KAAA,EAAuB;IAC7C,MAAM,QAAiB;QAAC,MAAM;KAAA;IAC9B,IAAI,iBAAiB,CAAA;IACrB,IAAI,eAA8B;IAElC,SAAS,kBAAkB,IAAA,EAAc,CAAA,EAAW,SAAA,EAAkB;QACpE;YACE,OAAQ,MAAM;gBACZ,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,eAAe;wBAC1B;oBACF;gBAEA,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,eAAe;wBACf,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,gBAAgB;wBAC3B;oBACF;gBAEA,KAAK;oBAAK;wBACR,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,eAAe;wBAC1B;oBACF;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,eAAe;wBAC1B;oBACF;gBAEA,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,qBAAqB;wBAChC;oBACF;gBAEA,KAAK;oBAAK;wBACR,iBAAiB;wBACjB,MAAM,GAAA,CAAI;wBACV,MAAM,IAAA,CAAK,SAAS;wBACpB,MAAM,IAAA,CAAK,oBAAoB;wBAC/B;oBACF;YACF;QACF;IACF;IAEA,SAAS,wBAAwB,IAAA,EAAc,CAAA,EAAW;QACxD,OAAQ,MAAM;YACZ,KAAK;gBAAK;oBACR,MAAM,GAAA,CAAI;oBACV,MAAM,IAAA,CAAK,2BAA2B;oBACtC;gBACF;YACA,KAAK;gBAAK;oBACR,iBAAiB;oBACjB,MAAM,GAAA,CAAI;oBACV;gBACF;QACF;IACF;IAEA,SAAS,uBAAuB,IAAA,EAAc,CAAA,EAAW;QACvD,OAAQ,MAAM;YACZ,KAAK;gBAAK;oBACR,MAAM,GAAA,CAAI;oBACV,MAAM,IAAA,CAAK,0BAA0B;oBACrC;gBACF;YACA,KAAK;gBAAK;oBACR,iBAAiB;oBACjB,MAAM,GAAA,CAAI;oBACV;gBACF;QACF;IACF;IAEA,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;QACrC,MAAM,OAAO,KAAA,CAAM,CAAC,CAAA;QACpB,MAAM,eAAe,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA;QAE3C,OAAQ,cAAc;YACpB,KAAK;gBACH,kBAAkB,MAAM,GAAG,QAAQ;gBACnC;YAEF,KAAK;gBAAuB;oBAC1B,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,mBAAmB;gCAC9B;4BACF;wBACA,KAAK;4BAAK;gCACR,iBAAiB;gCACjB,MAAM,GAAA,CAAI;gCACV;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAA6B;oBAChC,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,mBAAmB;gCAC9B;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAAqB;oBACxB,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,yBAAyB;gCACpC;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAA2B;oBAC9B,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,4BAA4B;gCAEvC;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAA8B;oBACjC,kBAAkB,MAAM,GAAG,2BAA2B;oBACtD;gBACF;YAEA,KAAK;gBAA6B;oBAChC,wBAAwB,MAAM,CAAC;oBAC/B;gBACF;YAEA,KAAK;gBAAiB;oBACpB,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,iBAAiB;gCACjB;4BACF;wBAEA,KAAK;4BAAM;gCACT,MAAM,IAAA,CAAK,sBAAsB;gCACjC;4BACF;wBAEA;4BAAS;gCACP,iBAAiB;4BACnB;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAAsB;oBACzB,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,iBAAiB;gCACjB,MAAM,GAAA,CAAI;gCACV;4BACF;wBAEA;4BAAS;gCACP,iBAAiB;gCACjB,kBAAkB,MAAM,GAAG,0BAA0B;gCACrD;4BACF;oBACF;oBACA;gBACF;YAEA,KAAK;gBAA4B;oBAC/B,OAAQ,MAAM;wBACZ,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCACV,MAAM,IAAA,CAAK,0BAA0B;gCACrC;4BACF;wBAEA,KAAK;4BAAK;gCACR,iBAAiB;gCACjB,MAAM,GAAA,CAAI;gCACV;4BACF;wBAEA;4BAAS;gCACP,iBAAiB;gCACjB;4BACF;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAA4B;oBAC/B,kBAAkB,MAAM,GAAG,0BAA0B;oBACrD;gBACF;YAEA,KAAK;gBAAwB;oBAC3B,MAAM,GAAA,CAAI;oBACV,iBAAiB;oBAEjB;gBACF;YAEA,KAAK;gBAAiB;oBACpB,OAAQ,MAAM;wBACZ,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BAAK;gCACR,iBAAiB;gCACjB;4BACF;wBAEA,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BAAK;gCACR;4BACF;wBAEA,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCAEV,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,4BAA4B;oCAC1D,uBAAuB,MAAM,CAAC;gCAChC;gCAEA,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,6BAA6B;oCAC3D,wBAAwB,MAAM,CAAC;gCACjC;gCAEA;4BACF;wBAEA,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCAEV,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,6BAA6B;oCAC3D,wBAAwB,MAAM,CAAC;gCACjC;gCAEA;4BACF;wBAEA,KAAK;4BAAK;gCACR,MAAM,GAAA,CAAI;gCAEV,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,4BAA4B;oCAC1D,uBAAuB,MAAM,CAAC;gCAChC;gCAEA;4BACF;wBAEA;4BAAS;gCACP,MAAM,GAAA,CAAI;gCACV;4BACF;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAAkB;oBACrB,MAAM,iBAAiB,MAAM,SAAA,CAAU,cAAe,IAAI,CAAC;oBAE3D,IACE,CAAC,QAAQ,UAAA,CAAW,cAAc,KAClC,CAAC,OAAO,UAAA,CAAW,cAAc,KACjC,CAAC,OAAO,UAAA,CAAW,cAAc,GACjC;wBACA,MAAM,GAAA,CAAI;wBAEV,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,6BAA6B;4BAC3D,wBAAwB,MAAM,CAAC;wBACjC,OAAA,IAAW,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,4BAA4B;4BACjE,uBAAuB,MAAM,CAAC;wBAChC;oBACF,OAAO;wBACL,iBAAiB;oBACnB;oBAEA;gBACF;QACF;IACF;IAEA,IAAI,SAAS,MAAM,KAAA,CAAM,GAAG,iBAAiB,CAAC;IAE9C,IAAA,IAAS,IAAI,MAAM,MAAA,GAAS,GAAG,KAAK,GAAG,IAAK;QAC1C,MAAM,QAAQ,KAAA,CAAM,CAAC,CAAA;QAErB,OAAQ,OAAO;YACb,KAAK;gBAAiB;oBACpB,UAAU;oBACV;gBACF;YAEA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAA6B;oBAChC,UAAU;oBACV;gBACF;YAEA,KAAK;YACL,KAAK;YACL,KAAK;gBAA4B;oBAC/B,UAAU;oBACV;gBACF;YAEA,KAAK;gBAAkB;oBACrB,MAAM,iBAAiB,MAAM,SAAA,CAAU,cAAe,MAAM,MAAM;oBAElE,IAAI,OAAO,UAAA,CAAW,cAAc,GAAG;wBACrC,UAAU,OAAO,KAAA,CAAM,eAAe,MAAM;oBAC9C,OAAA,IAAW,QAAQ,UAAA,CAAW,cAAc,GAAG;wBAC7C,UAAU,QAAQ,KAAA,CAAM,eAAe,MAAM;oBAC/C,OAAA,IAAW,OAAO,UAAA,CAAW,cAAc,GAAG;wBAC5C,UAAU,OAAO,KAAA,CAAM,eAAe,MAAM;oBAC9C;gBACF;QACF;IACF;IAEA,OAAO;AACT;;AD5YO,SAAS,iBAAiB,QAAA,EAO/B;IACA,IAAI,aAAa,KAAA,GAAW;QAC1B,OAAO;YAAE,OAAO,KAAA;YAAW,OAAO;QAAkB;IACtD;IAEA,IAAI,0LAAS,gBAAA,EAAc;QAAE,MAAM;IAAS,CAAC;IAE7C,IAAI,OAAO,OAAA,EAAS;QAClB,OAAO;YAAE,OAAO,OAAO,KAAA;YAAO,OAAO;QAAmB;IAC1D;IAEA,0LAAS,gBAAA,EAAc;QAAE,MAAM,QAAQ,QAAQ;IAAE,CAAC;IAElD,IAAI,OAAO,OAAA,EAAS;QAClB,OAAO;YAAE,OAAO,OAAO,KAAA;YAAO,OAAO;QAAiB;IACxD;IAEA,OAAO;QAAE,OAAO,KAAA;QAAW,OAAO;IAAe;AACnD;;AETA,IAAMA,kBAAsD;IAC1D,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,qCAAqC;QACvD;QACA,OAAO;YAAE,MAAM;YAAQ;QAAM;IAC/B;AACF;AAEA,IAAM,iBAAgE;IACpE,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,CAAC,MAAM,OAAA,CAAQ,KAAK,GAAG;YACzB,MAAM,IAAI,MAAM,qCAAqC;QACvD;QAEA,OAAO;YAAE,MAAM;YAAQ;QAAM;IAC/B;AACF;AAEA,IAAMC,mBAAwD;IAC5D,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,sCAAsC;QACxD;QACA,OAAO;YAAE,MAAM;YAAS;QAAM;IAChC;AACF;AAEA,IAAM,+BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,CAAC,MAAM,OAAA,CAAQ,KAAK,GAAG;YACzB,MAAM,IAAI,MAAM,oDAAoD;QACtE;QAEA,OAAO;YAAE,MAAM;YAAuB;QAAM;IAC9C;AACF;AAEA,IAAM,qBAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,gBAAgB,KAAA,KAClB,OAAO,MAAM,UAAA,KAAe,YAC5B,CAAA,CAAE,cAAc,KAAA,KAChB,OAAO,MAAM,QAAA,KAAa,YAC1B,CAAA,CAAE,UAAU,KAAA,KACZ,OAAO,MAAM,IAAA,KAAS,UACtB;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,uBAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,gBAAgB,KAAA,KAClB,OAAO,MAAM,UAAA,KAAe,YAC5B,CAAA,CAAE,YAAY,KAAA,GACd;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QAIF;IACF;AACF;AAEA,IAAM,mCAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,gBAAgB,KAAA,KAClB,OAAO,MAAM,UAAA,KAAe,YAC5B,CAAA,CAAE,cAAc,KAAA,KAChB,OAAO,MAAM,QAAA,KAAa,UAC1B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,0BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,gBAAgB,KAAA,KAClB,OAAO,MAAM,UAAA,KAAe,YAC5B,CAAA,CAAE,mBAAmB,KAAA,KACrB,OAAO,MAAM,aAAA,KAAkB,UAC/B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN;QAIF;IACF;AACF;AAEA,IAAM,0BAWF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,kBAAkB,KAAA,KACpB,OAAO,MAAM,YAAA,KAAiB,UAC9B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,MAAM,SAMF;YACF,cAAc,MAAM,YAAA;QACtB;QAEA,IACE,WAAW,SACX,MAAM,KAAA,IAAS,QACf,OAAO,MAAM,KAAA,KAAU,YACvB,kBAAkB,MAAM,KAAA,IACxB,sBAAsB,MAAM,KAAA,EAC5B;YACA,OAAO,KAAA,GAAQ;gBACb,cACE,OAAO,MAAM,KAAA,CAAM,YAAA,KAAiB,WAChC,MAAM,KAAA,CAAM,YAAA,GACZ,OAAO,GAAA;gBACb,kBACE,OAAO,MAAM,KAAA,CAAM,gBAAA,KAAqB,WACpC,MAAM,KAAA,CAAM,gBAAA,GACZ,OAAO,GAAA;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;AACF;AAEA,IAAM,uBAWF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,kBAAkB,KAAA,KACpB,OAAO,MAAM,YAAA,KAAiB,UAC9B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,MAAM,SAOF;YACF,cAAc,MAAM,YAAA;YACpB,aAAa;QACf;QAEA,IACE,WAAW,SACX,MAAM,KAAA,IAAS,QACf,OAAO,MAAM,KAAA,KAAU,YACvB,kBAAkB,MAAM,KAAA,IACxB,sBAAsB,MAAM,KAAA,EAC5B;YACA,OAAO,KAAA,GAAQ;gBACb,cACE,OAAO,MAAM,KAAA,CAAM,YAAA,KAAiB,WAChC,MAAM,KAAA,CAAM,YAAA,GACZ,OAAO,GAAA;gBACb,kBACE,OAAO,MAAM,KAAA,CAAM,gBAAA,KAAqB,WACpC,MAAM,KAAA,CAAM,gBAAA,GACZ,OAAO,GAAA;YACf;QACF;QAEA,IAAI,iBAAiB,SAAS,OAAO,MAAM,WAAA,KAAgB,WAAW;YACpE,OAAO,WAAA,GAAc,MAAM,WAAA;QAC7B;QAEA,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;AACF;AAEA,IAAM,sBAMF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,eAAe,KAAA,KACjB,OAAO,MAAM,SAAA,KAAc,UAC3B;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,MAAM;YACN,OAAO;gBACL,WAAW,MAAM,SAAA;YACnB;QACF;IACF;AACF;AAEA,IAAM,sBAAgE;IACpE,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,IAAI,MAAM,0CAA0C;QAC5D;QACA,OAAO;YAAE,MAAM;YAAa;QAAM;IACpC;AACF;AAEA,IAAM,aAAmE;IACvE,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IAAI,SAAS,QAAQ,OAAO,UAAU,UAAU;YAC9C,MAAM,IAAI,MAAM,wCAAwC;QAC1D;QAEA,OAAO;YACL,MAAM;YACN;QACF;IACF;AACF;AAEA,IAAM,8BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,UAAU,KAAA,KACZ,OAAO,MAAM,IAAA,KAAS,UACtB;YACA,MAAM,IAAI,MACR;QAEJ;QACA,OAAO;YAAE,MAAM;YAAsB,OAAO;gBAAE,MAAM,MAAM,IAAA;YAAK;QAAE;IACnE;AACF;AAEA,IAAM,+BAIF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,eAAe,KAAA,KACjB,OAAO,MAAM,SAAA,KAAc,UAC3B;YACA,MAAM,IAAI,MACR;QAEJ;QACA,OAAO;YACL,MAAM;YACN,OAAO;gBAAE,WAAW,MAAM,SAAA;YAAU;QACtC;IACF;AACF;AAEA,IAAM,iBAOF;IACF,MAAM;IACN,MAAM;IACN,OAAO,CAAC,UAAqB;QAC3B,IACE,SAAS,QACT,OAAO,UAAU,YACjB,CAAA,CAAE,UAAU,KAAA,KACZ,OAAO,MAAM,IAAA,KAAS,YACtB,CAAA,CAAE,cAAc,KAAA,KAChB,OAAO,MAAM,QAAA,KAAa,UAC1B;YACA,MAAM,IAAI,MACR;QAEJ;QACA,OAAO;YAAE,MAAM;YAAQ;QAAmD;IAC5E;AACF;AAEA,IAAM,kBAAkB;IACtBD;IACA;IACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAEO,IAAM,wBAAwB,OAAO,WAAA,CAC1C,gBAAgB,GAAA,CAAI,CAAA,OAAQ;QAAC,KAAK,IAAA;QAAM,IAAI;KAAC;AAsCxC,IAAM,2BAA2B,OAAO,WAAA,CAC7C,gBAAgB,GAAA,CAAI,CAAA,OAAQ;QAAC,KAAK,IAAA;QAAM,KAAK,IAAI;KAAC;AAK7C,IAAMC,cAAa,gBAAgB,GAAA,CAAI,CAAA,OAAQ,KAAK,IAAI;AASxD,IAAM,sBAAsB,CAAC,SAAqC;IACvE,MAAM,sBAAsB,KAAK,OAAA,CAAQ,GAAG;IAE5C,IAAI,wBAAwB,CAAA,GAAI;QAC9B,MAAM,IAAI,MAAM,oDAAoD;IACtE;IAEA,MAAM,SAAS,KAAK,KAAA,CAAM,GAAG,mBAAmB;IAEhD,IAAI,CAACA,YAAW,QAAA,CAAS,MAA4C,GAAG;QACtE,MAAM,IAAI,MAAM,CAAA,4CAAA,EAA+C,MAAM,CAAA,CAAA,CAAG;IAC1E;IAEA,MAAM,OAAO;IAEb,MAAM,YAAY,KAAK,KAAA,CAAM,sBAAsB,CAAC;IACpD,MAAM,YAAuB,KAAK,KAAA,CAAM,SAAS;IAEjD,OAAO,qBAAA,CAAsB,IAAI,CAAA,CAAE,KAAA,CAAM,SAAS;AACpD;AAQO,SAAS,qBACd,IAAA,EACA,KAAA,EACkB;IAClB,MAAM,aAAa,gBAAgB,IAAA,CAAK,CAAA,OAAQ,KAAK,IAAA,KAAS,IAAI;IAElE,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,IAAI,EAAE;IACrD;IAEA,OAAO,GAAG,WAAW,IAAI,CAAA,CAAA,EAAI,KAAK,SAAA,CAAU,KAAK,CAAC,CAAA;AAAA,CAAA;AACpD;;AC9iBA,IAAM,UAAU,KAAK,UAAA,CAAW,CAAC;AAGjC,SAAS,aAAa,MAAA,EAAsB,WAAA,EAAqB;IAC/D,MAAM,qBAAqB,IAAI,WAAW,WAAW;IAErD,IAAI,SAAS;IACb,KAAA,MAAW,SAAS,OAAQ;QAC1B,mBAAmB,GAAA,CAAI,OAAO,MAAM;QACpC,UAAU,MAAM,MAAA;IAClB;IACA,OAAO,MAAA,GAAS;IAEhB,OAAO;AACT;AAEA,eAAsB,kBAAkB,EACtC,MAAA,EACA,UAAA,EACA,eAAA,EACA,wBAAA,EACA,uBAAA,EACA,YAAA,EACA,UAAA,EACA,UAAA,EACA,WAAA,EACA,4BAAA,EACA,mBAAA,EACA,cAAA,EACA,gBAAA,EACA,wBAAA,EACA,mBAAA,EACA,gBAAA,EACA,eAAA,EACF,EAsDkB;IAIhB,MAAM,SAAS,OAAO,SAAA,CAAU;IAChC,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,SAAuB,CAAC,CAAA;IAC9B,IAAI,cAAc;IAElB,MAAO,KAAM;QACX,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,IAAA,CAAK;QAEpC,IAAI,OAAO;YACT,OAAO,IAAA,CAAK,KAAK;YACjB,eAAe,MAAM,MAAA;YACrB,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAM,SAAS;gBAEvC;YACF;QACF;QAEA,IAAI,OAAO,MAAA,KAAW,GAAG;YACvB;QACF;QAEA,MAAM,qBAAqB,aAAa,QAAQ,WAAW;QAC3D,cAAc;QAEd,MAAM,cAAc,QACjB,MAAA,CAAO,oBAAoB;YAAE,QAAQ;QAAK,CAAC,EAC3C,KAAA,CAAM,IAAI,EACV,MAAA,CAAO,CAAA,OAAQ,SAAS,EAAE,EAC1B,GAAA,CAAI,mBAAmB;QAE1B,KAAA,MAAW,EAAE,IAAA,EAAM,OAAAC,MAAAA,CAAM,CAAA,IAAK,YAAa;YACzC,OAAQ,MAAM;gBACZ,KAAK;oBACH,MAAA,CAAM,cAAA,OAAA,KAAA,IAAA,WAAaA,OAAAA;oBACnB;gBACF,KAAK;oBACH,MAAA,CAAM,mBAAA,OAAA,KAAA,IAAA,gBAAkBA,OAAAA;oBACxB;gBACF,KAAK;oBACH,MAAA,CAAM,4BAAA,OAAA,KAAA,IAAA,yBAA2BA,OAAAA;oBACjC;gBACF,KAAK;oBACH,MAAA,CAAM,2BAAA,OAAA,KAAA,IAAA,wBAA0BA,OAAAA;oBAChC;gBACF,KAAK;oBACH,MAAA,CAAM,cAAA,OAAA,KAAA,IAAA,WAAaA,OAAAA;oBACnB;gBACF,KAAK;oBACH,MAAA,CAAM,gBAAA,OAAA,KAAA,IAAA,aAAeA,OAAAA;oBACrB;gBACF,KAAK;oBACH,MAAA,CAAM,cAAA,OAAA,KAAA,IAAA,WAAaA,OAAAA;oBACnB;gBACF,KAAK;oBACH,MAAA,CAAM,eAAA,OAAA,KAAA,IAAA,YAAcA,OAAAA;oBACpB;gBACF,KAAK;oBACH,MAAA,CAAM,4BAAA,OAAA,KAAA,IAAA,yBAA2BA,OAAAA;oBACjC;gBACF,KAAK;oBACH,MAAA,CAAM,gCAAA,OAAA,KAAA,IAAA,6BAA+BA,OAAAA;oBACrC;gBACF,KAAK;oBACH,MAAA,CAAM,uBAAA,OAAA,KAAA,IAAA,oBAAsBA,OAAAA;oBAC5B;gBACF,KAAK;oBACH,MAAA,CAAM,kBAAA,OAAA,KAAA,IAAA,eAAiBA,OAAAA;oBACvB;gBACF,KAAK;oBACH,MAAA,CAAM,oBAAA,OAAA,KAAA,IAAA,iBAAmBA,OAAAA;oBACzB;gBACF,KAAK;oBACH,MAAA,CAAM,uBAAA,OAAA,KAAA,IAAA,oBAAsBA,OAAAA;oBAC5B;gBACF,KAAK;oBACH,MAAA,CAAM,oBAAA,OAAA,KAAA,IAAA,iBAAmBA,OAAAA;oBACzB;gBACF,KAAK;oBACH,MAAA,CAAM,mBAAA,OAAA,KAAA,IAAA,gBAAkBA,OAAAA;oBACxB;gBACF;oBAAS;wBACP,MAAM,kBAAyB;wBAC/B,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,eAAe,EAAE;oBAChE;YACF;QACF;IACF;AACF;;ALnKA,eAAsB,oBAAoB,EACxC,MAAA,EACA,MAAA,EACA,UAAA,EACA,QAAA,EACA,YAAAC,2LAAa,aAAA,EACb,iBAAiB,IAAM,aAAA,GAAA,IAAI,KAAK,CAAA,EAChC,WAAA,EACF,EAgBG;IA1CH,IAAA,IAAA;IA2CE,MAAM,qBAAA,CAAqB,eAAA,OAAA,KAAA,IAAA,YAAa,IAAA,MAAS;IACjD,IAAI,OAAO,qBACP,IAAA,8CAAA;IAAA,CAAA,CAEC,KAAA,CAAA,KAAA,YAAY,eAAA,KAAZ,OAAA,KAAA,IAAA,GAA6B,MAAA,CAAO,CAAC,KAAK,mBAAmB;QA/CpE,IAAAC;QAgDQ,OAAO,KAAK,GAAA,CAAI,KAAA,CAAKA,MAAA,eAAe,IAAA,KAAf,OAAAA,MAAuB,CAAC;IAC/C,GAAG,EAAA,KAFF,OAAA,KAEQ,CAAA,IACT;IAEJ,MAAM,UAAqB,qBACvB,gBAAgB,WAAW,IAC3B;QACE,IAAID,YAAW;QACf,WAAW,eAAe;QAC1B,MAAM;QACN,SAAS;QACT,OAAO,CAAC,CAAA;IACV;IAEJ,IAAI,kBAA0C,KAAA;IAC9C,IAAI,uBAAoD,KAAA;IACxD,IAAI,6BAEY,KAAA;IAEhB,SAAS,yBACP,UAAA,EACA,UAAA,EACA;QACA,MAAM,OAAO,QAAQ,KAAA,CAAM,IAAA,CACzB,CAAAE,QACEA,MAAK,IAAA,KAAS,qBACdA,MAAK,cAAA,CAAe,UAAA,KAAe;QAGvC,IAAI,QAAQ,MAAM;YAChB,KAAK,cAAA,GAAiB;QACxB,OAAO;YACL,QAAQ,KAAA,CAAM,IAAA,CAAK;gBACjB,MAAM;gBACN,gBAAgB;YAClB,CAAC;QACH;IACF;IAEA,MAAM,OAAoB,CAAC,CAAA;IAG3B,IAAI,qBAA8C,qBAC9C,eAAA,OAAA,KAAA,IAAA,YAAa,WAAA,GACb,KAAA;IAGJ,MAAM,mBAGF,CAAC;IAEL,IAAI,QAA4B;QAC9B,kBAAkB;QAClB,cAAc;QACd,aAAa;IACf;IACA,IAAI,eAA4C;IAEhD,SAAS,aAAa;QAEpB,MAAM,aAAa,CAAC;eAAG,IAAI;SAAA;QAI3B,IAAI,sBAAA,OAAA,KAAA,IAAA,mBAAoB,MAAA,EAAQ;YAC9B,QAAQ,WAAA,GAAc;QACxB;QAEA,MAAM,gBAAgB;YAAA,kFAAA;YAAA,uFAAA;YAGpB,GAAG,gBAAgB,OAAO,CAAA;YAAA,+EAAA;YAAA,+EAAA;YAAA,+EAAA;YAAA,4EAAA;YAAA,2BAAA;YAM1B,YAAYF,YAAW;QACzB;QAEA,OAAO;YACL,SAAS;YACT,MAAM;YACN;QACF,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB;QACA,YAAW,KAAA,EAAO;YAChB,IAAI,mBAAmB,MAAM;gBAC3B,kBAAkB;oBAChB,MAAM;oBACN,MAAM;gBACR;gBACA,QAAQ,KAAA,CAAM,IAAA,CAAK,eAAe;YACpC,OAAO;gBACL,gBAAgB,IAAA,IAAQ;YAC1B;YAEA,QAAQ,OAAA,IAAW;YACnB,WAAW;QACb;QACA,iBAAgB,KAAA,EAAO;YAzJ3B,IAAAC;YA0JM,IAAI,8BAA8B,MAAM;gBACtC,6BAA6B;oBAAE,MAAM;oBAAQ,MAAM;gBAAM;gBACzD,IAAI,wBAAwB,MAAM;oBAChC,qBAAqB,OAAA,CAAQ,IAAA,CAAK,0BAA0B;gBAC9D;YACF,OAAO;gBACL,2BAA2B,IAAA,IAAQ;YACrC;YAEA,IAAI,wBAAwB,MAAM;gBAChC,uBAAuB;oBACrB,MAAM;oBACN,WAAW;oBACX,SAAS;wBAAC,0BAA0B;qBAAA;gBACtC;gBACA,QAAQ,KAAA,CAAM,IAAA,CAAK,oBAAoB;YACzC,OAAO;gBACL,qBAAqB,SAAA,IAAa;YACpC;YAEA,QAAQ,SAAA,GAAA,CAAA,CAAaA,MAAA,QAAQ,SAAA,KAAR,OAAAA,MAAqB,EAAA,IAAM;YAEhD,WAAW;QACb;QACA,0BAAyB,KAAA,EAAO;YAC9B,IAAI,8BAA8B,MAAM;gBACtC,2BAA2B,SAAA,GAAY,MAAM,SAAA;YAC/C;QACF;QACA,yBAAwB,KAAA,EAAO;YAC7B,IAAI,wBAAwB,MAAM;gBAChC,uBAAuB;oBACrB,MAAM;oBACN,WAAW;oBACX,SAAS,CAAC,CAAA;gBACZ;gBACA,QAAQ,KAAA,CAAM,IAAA,CAAK,oBAAoB;YACzC;YAEA,qBAAqB,OAAA,CAAQ,IAAA,CAAK;gBAChC,MAAM;gBACN,MAAM,MAAM,IAAA;YACd,CAAC;YAED,6BAA6B,KAAA;YAE7B,WAAW;QACb;QACA,YAAW,KAAA,EAAO;YAChB,QAAQ,KAAA,CAAM,IAAA,CAAK;gBACjB,MAAM;gBACN,UAAU,MAAM,QAAA;gBAChB,MAAM,MAAM,IAAA;YACd,CAAC;YAED,WAAW;QACb;QACA,cAAa,KAAA,EAAO;YAClB,QAAQ,KAAA,CAAM,IAAA,CAAK;gBACjB,MAAM;gBACN,QAAQ;YACV,CAAC;YAED,WAAW;QACb;QACA,8BAA6B,KAAA,EAAO;YAClC,IAAI,QAAQ,eAAA,IAAmB,MAAM;gBACnC,QAAQ,eAAA,GAAkB,CAAC,CAAA;YAC7B;YAGA,gBAAA,CAAiB,MAAM,UAAU,CAAA,GAAI;gBACnC,MAAM;gBACN;gBACA,UAAU,MAAM,QAAA;gBAChB,OAAO,QAAQ,eAAA,CAAgB,MAAA;YACjC;YAEA,MAAM,aAAa;gBACjB,OAAO;gBACP;gBACA,YAAY,MAAM,UAAA;gBAClB,UAAU,MAAM,QAAA;gBAChB,MAAM,KAAA;YACR;YAEA,QAAQ,eAAA,CAAgB,IAAA,CAAK,UAAU;YAEvC,yBAAyB,MAAM,UAAA,EAAY,UAAU;YAErD,WAAW;QACb;QACA,qBAAoB,KAAA,EAAO;YACzB,MAAM,kBAAkB,gBAAA,CAAiB,MAAM,UAAU,CAAA;YAEzD,gBAAgB,IAAA,IAAQ,MAAM,aAAA;YAE9B,MAAM,EAAE,OAAO,WAAA,CAAY,CAAA,GAAI,iBAAiB,gBAAgB,IAAI;YAEpE,MAAM,aAAa;gBACjB,OAAO;gBACP,MAAM,gBAAgB,IAAA;gBACtB,YAAY,MAAM,UAAA;gBAClB,UAAU,gBAAgB,QAAA;gBAC1B,MAAM;YACR;YAEA,QAAQ,eAAA,CAAiB,gBAAgB,KAAK,CAAA,GAAI;YAElD,yBAAyB,MAAM,UAAA,EAAY,UAAU;YAErD,WAAW;QACb;QACA,MAAM,gBAAe,KAAA,EAAO;YAC1B,MAAM,aAAa;gBACjB,OAAO;gBACP;gBACA,GAAG,KAAA;YACL;YAEA,IAAI,gBAAA,CAAiB,MAAM,UAAU,CAAA,IAAK,MAAM;gBAE9C,QAAQ,eAAA,CAAiB,gBAAA,CAAiB,MAAM,UAAU,CAAA,CAAE,KAAK,CAAA,GAC/D;YACJ,OAAO;gBACL,IAAI,QAAQ,eAAA,IAAmB,MAAM;oBACnC,QAAQ,eAAA,GAAkB,CAAC,CAAA;gBAC7B;gBAEA,QAAQ,eAAA,CAAgB,IAAA,CAAK,UAAU;YACzC;YAEA,yBAAyB,MAAM,UAAA,EAAY,UAAU;YAErD,WAAW;YAKX,IAAI,YAAY;gBACd,MAAM,SAAS,MAAM,WAAW;oBAAE,UAAU;gBAAM,CAAC;gBACnD,IAAI,UAAU,MAAM;oBAClB,MAAME,cAAa;wBACjB,OAAO;wBACP;wBACA,GAAG,KAAA;wBACH;oBACF;oBAGA,QAAQ,eAAA,CAAiB,QAAQ,eAAA,CAAiB,MAAA,GAAS,CAAC,CAAA,GAC1DA;oBAEF,yBAAyB,MAAM,UAAA,EAAYA,WAAU;oBAErD,WAAW;gBACb;YACF;QACF;QACA,kBAAiB,KAAA,EAAO;YACtB,MAAM,kBAAkB,QAAQ,eAAA;YAEhC,IAAI,mBAAmB,MAAM;gBAC3B,MAAM,IAAI,MAAM,6CAA6C;YAC/D;YAIA,MAAM,sBAAsB,gBAAgB,SAAA,CAC1C,CAAAA,cAAcA,YAAW,UAAA,KAAe,MAAM,UAAA;YAGhD,IAAI,wBAAwB,CAAA,GAAI;gBAC9B,MAAM,IAAI,MACR;YAEJ;YAEA,MAAM,aAAa;gBACjB,GAAG,eAAA,CAAgB,mBAAmB,CAAA;gBACtC,OAAO;gBACP,GAAG,KAAA;YACL;YAEA,eAAA,CAAgB,mBAAmB,CAAA,GAAI;YAEvC,yBAAyB,MAAM,UAAA,EAAY,UAAU;YAErD,WAAW;QACb;QACA,YAAW,KAAA,EAAO;YAChB,KAAK,IAAA,CAAK,GAAG,KAAK;YAClB,WAAW;QACb;QACA,0BAAyB,KAAA,EAAO;YAC9B,IAAI,sBAAsB,MAAM;gBAC9B,qBAAqB,CAAC;uBAAG,KAAK;iBAAA;YAChC,OAAO;gBACL,mBAAmB,IAAA,CAAK,GAAG,KAAK;YAClC;YAEA,WAAW;QACb;QACA,kBAAiB,KAAA,EAAO;YACtB,QAAQ;YAGR,kBAAkB,MAAM,WAAA,GAAc,kBAAkB,KAAA;YACxD,uBAAuB,KAAA;YACvB,6BAA6B,KAAA;QAC/B;QACA,iBAAgB,KAAA,EAAO;YAErB,IAAI,CAAC,oBAAoB;gBACvB,QAAQ,EAAA,GAAK,MAAM,SAAA;YACrB;YAGA,QAAQ,KAAA,CAAM,IAAA,CAAK;gBAAE,MAAM;YAAa,CAAC;YACzC,WAAW;QACb;QACA,qBAAoB,KAAA,EAAO;YACzB,eAAe,MAAM,YAAA;YACrB,IAAI,MAAM,KAAA,IAAS,MAAM;gBACvB,QAAQ,4BAA4B,MAAM,KAAK;YACjD;QACF;QACA,aAAY,KAAA,EAAO;YACjB,MAAM,IAAI,MAAM,KAAK;QACvB;IACF,CAAC;IAED,YAAA,OAAA,KAAA,IAAA,SAAW;QAAE;QAAS;QAAc;IAAM;AAC5C;;;AOnYA,eAAsB,kBAAkB,EACtC,MAAA,EACA,UAAA,EACF,EAGkB;IAChB,MAAM,SAAS,OAAO,WAAA,CAAY,IAAI,kBAAkB,CAAC,EAAE,SAAA,CAAU;IACrE,MAAO,KAAM;QACX,MAAM,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,IAAA,CAAK;QAC1C,IAAI,MAAM;YACR;QACF;QACA,MAAM,WAAW,KAAK;IACxB;AACF;;ADVA,eAAsB,wBAAwB,EAC5C,MAAA,EACA,MAAA,EACA,QAAA,EACA,iBAAiB,IAAM,aAAA,GAAA,IAAI,KAAK,CAAA,EAChC,YAAAE,0LAAaC,cAAAA,EACf,EAUG;IACD,MAAM,WAAuB;QAAE,MAAM;QAAQ,MAAM;IAAG;IAEtD,MAAM,gBAA2B;QAC/B,IAAID,YAAW;QACf,WAAW,eAAe;QAC1B,MAAM;QACN,SAAS;QACT,OAAO;YAAC,QAAQ;SAAA;IAClB;IAEA,MAAM,kBAAkB;QACtB;QACA,YAAY,CAAA,UAAS;YACnB,cAAc,OAAA,IAAW;YACzB,SAAS,IAAA,IAAQ;YAGjB,OAAO;gBACL,SAAS;oBAAE,GAAG,aAAA;gBAAc;gBAC5B,MAAM,CAAC,CAAA;gBACP,oBAAoB;YACtB,CAAC;QACH;IACF,CAAC;IAGD,YAAA,OAAA,KAAA,IAAA,SAAW,eAAe;QACxB,OAAO;YAAE,kBAAkB;YAAK,cAAc;YAAK,aAAa;QAAI;QACpE,cAAc;IAChB;AACF;;AE/CA,IAAM,mBAAmB,IAAM;AAE/B,eAAsB,YAAY,EAChC,GAAA,EACA,IAAA,EACA,iBAAiB,MAAA,EACjB,WAAA,EACA,OAAA,EACA,eAAA,EACA,wBAAA,EACA,UAAA,EACA,QAAA,EACA,QAAA,EACA,UAAA,EACA,YAAAE,WAAAA,EACA,OAAAC,SAAQ,iBAAiB,CAAA,EACzB,WAAA,EACA,cAAc,UAAA,EAChB,EAoBG;IA3CH,IAAA,IAAA,IAAA;IA4CE,MAAM,UACJ,gBAAgB,WACZA,OAAM,GAAG,GAAG,CAAA,QAAA,EAAW,KAAK,EAAE,EAAA,EAAI;QAChC,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,GAAG,OAAA;QACL;QACA,QAAA,CAAQ,KAAA,mBAAA,OAAA,KAAA,IAAA,iBAAA,KAAA,OAAA,KAAA,IAAA,GAAqB,MAAA;QAC7B;IACF,CAAC,IACDA,OAAM,KAAK;QACT,QAAQ;QACR,MAAM,KAAK,SAAA,CAAU,IAAI;QACzB,SAAS;YACP,gBAAgB;YAChB,GAAG,OAAA;QACL;QACA,QAAA,CAAQ,KAAA,mBAAA,OAAA,KAAA,IAAA,iBAAA,KAAA,OAAA,KAAA,IAAA,GAAqB,MAAA;QAC7B;IACF,CAAC;IAEP,MAAM,WAAW,MAAM,QAAQ,KAAA,CAAM,CAAA,QAAO;QAC1C,yBAAyB;QACzB,MAAM;IACR,CAAC;IAED,IAAI,YAAY;QACd,IAAI;YACF,MAAM,WAAW,QAAQ;QAC3B,EAAA,OAAS,KAAK;YACZ,MAAM;QACR;IACF;IAEA,IAAI,CAAC,SAAS,EAAA,EAAI;QAChB,yBAAyB;QACzB,MAAM,IAAI,MAAA,CACP,KAAA,MAAM,SAAS,IAAA,CAAK,CAAA,KAApB,OAAA,KAA0B;IAE/B;IAEA,IAAI,CAAC,SAAS,IAAA,EAAM;QAClB,MAAM,IAAI,MAAM,6BAA6B;IAC/C;IAEA,OAAQ,gBAAgB;QACtB,KAAK;YAAQ;gBACX,MAAM,wBAAwB;oBAC5B,QAAQ,SAAS,IAAA;oBACjB,QAAQ;oBACR;oBACA,YAAAD;gBACF,CAAC;gBACD;YACF;QAEA,KAAK;YAAQ;gBACX,MAAM,oBAAoB;oBACxB,QAAQ,SAAS,IAAA;oBACjB,QAAQ;oBACR;oBACA;oBACA,UAAS,EAAE,OAAA,EAAS,YAAA,EAAc,KAAA,CAAM,CAAA,EAAG;wBACzC,IAAI,YAAY,WAAW,MAAM;4BAC/B,SAAS,SAAS;gCAAE;gCAAO;4BAAa,CAAC;wBAC3C;oBACF;oBACA,YAAAA;gBACF,CAAC;gBACD;YACF;QAEA;YAAS;gBACP,MAAM,kBAAyB;gBAC/B,MAAM,IAAI,MAAM,CAAA,yBAAA,EAA4B,eAAe,EAAE;YAC/D;IACF;AACF;;ACrHA,IAAME,oBAAmB,IAAM;AAE/B,eAAsB,kBAAkB,EACtC,GAAA,EACA,MAAA,EACA,WAAA,EACA,OAAA,EACA,IAAA,EACA,iBAAiB,MAAA,EACjB,aAAA,EACA,UAAA,EACA,QAAA,EACA,kBAAA,EACA,UAAA,EACA,QAAA,EACA,OAAA,EACA,MAAA,EACA,OAAAC,SAAQD,kBAAiB,CAAA,EAC3B,EAgBG;IAvCH,IAAA;IAwCE,IAAI;QACF,WAAW,IAAI;QACf,SAAS,KAAA,CAAS;QAElB,MAAM,kBAAkB,IAAI,gBAAgB;QAC5C,mBAAmB,eAAe;QAGlC,cAAc,EAAE;QAEhB,MAAM,WAAW,MAAMC,OAAM,KAAK;YAChC,QAAQ;YACR,MAAM,KAAK,SAAA,CAAU;gBACnB;gBACA,GAAG,IAAA;YACL,CAAC;YACD;YACA,SAAS;gBACP,gBAAgB;gBAChB,GAAG,OAAA;YACL;YACA,QAAQ,gBAAgB,MAAA;QAC1B,CAAC,EAAE,KAAA,CAAM,CAAA,QAAO;YACd,MAAM;QACR,CAAC;QAED,IAAI,YAAY;YACd,IAAI;gBACF,MAAM,WAAW,QAAQ;YAC3B,EAAA,OAAS,KAAK;gBACZ,MAAM;YACR;QACF;QAEA,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,MAAM,IAAI,MAAA,CACP,KAAA,MAAM,SAAS,IAAA,CAAK,CAAA,KAApB,OAAA,KAA0B;QAE/B;QAEA,IAAI,CAAC,SAAS,IAAA,EAAM;YAClB,MAAM,IAAI,MAAM,6BAA6B;QAC/C;QAEA,IAAI,SAAS;QAEb,OAAQ,gBAAgB;YACtB,KAAK;gBAAQ;oBACX,MAAM,kBAAkB;wBACtB,QAAQ,SAAS,IAAA;wBACjB,YAAY,CAAA,UAAS;4BACnB,UAAU;4BACV,cAAc,MAAM;wBACtB;oBACF,CAAC;oBACD;gBACF;YACA,KAAK;gBAAQ;oBACX,MAAM,kBAAkB;wBACtB,QAAQ,SAAS,IAAA;wBACjB,YAAW,KAAA,EAAO;4BAChB,UAAU;4BACV,cAAc,MAAM;wBACtB;wBACA,YAAW,KAAA,EAAO;4BAChB,UAAA,OAAA,KAAA,IAAA,OAAS;wBACX;wBACA,aAAY,KAAA,EAAO;4BACjB,MAAM,IAAI,MAAM,KAAK;wBACvB;oBACF,CAAC;oBACD;gBACF;YACA;gBAAS;oBACP,MAAM,kBAAyB;oBAC/B,MAAM,IAAI,MAAM,CAAA,yBAAA,EAA4B,eAAe,EAAE;gBAC/D;QACF;QAEA,IAAI,UAAU;YACZ,SAAS,QAAQ,MAAM;QACzB;QAEA,mBAAmB,IAAI;QACvB,OAAO;IACT,EAAA,OAAS,KAAK;QAEZ,IAAK,IAAY,IAAA,KAAS,cAAc;YACtC,mBAAmB,IAAI;YACvB,OAAO;QACT;QAEA,IAAI,eAAe,OAAO;YACxB,IAAI,SAAS;gBACX,QAAQ,GAAG;YACb;QACF;QAEA,SAAS,GAAY;IACvB,SAAE;QACA,WAAW,KAAK;IAClB;AACF;;AC3IO,SAAS,mBAAmB,OAAA,EAAyB;IAC1D,MAAM,CAAC,QAAQ,aAAa,CAAA,GAAI,QAAQ,KAAA,CAAM,GAAG;IACjD,MAAM,WAAW,OAAO,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA;IAElD,IAAI,YAAY,QAAQ,iBAAiB,MAAM;QAC7C,MAAM,IAAI,MAAM,yBAAyB;IAC3C;IAEA,IAAI;QACF,OAAO,OAAO,IAAA,CAAK,aAAa;IAClC,EAAA,OAAS,OAAO;QACd,MAAM,IAAI,MAAM,CAAA,uBAAA,CAAyB;IAC3C;AACF;;ACdO,SAAS,6BACd,eAAA,EACoB;IACpB,OAAO,mBAAA,OAAA,KAAA,IAAA,gBAAiB,MAAA,CAAO,CAAC,KAAK,mBAAmB;QAL1D,IAAA;QAMI,OAAO,KAAK,GAAA,CAAI,KAAA,CAAK,KAAA,eAAe,IAAA,KAAf,OAAA,KAAuB,CAAC;IAC/C,GAAG;AACL;;ACIO,SAAS,gBACd,OAAA,EAQE;IArBJ,IAAA;IAsBE,OAAA,CACE,KAAA,QAAQ,KAAA,KAAR,OAAA,KAAiB;WACX,QAAQ,eAAA,GACR,QAAQ,eAAA,CAAgB,GAAA,CAAI,CAAA,iBAAA,CAAmB;gBAC7C,MAAM;gBACN;YACF,CAAA,CAAE,IACF,CAAC,CAAA;WACD,QAAQ,SAAA,GACR;YACE;gBACE,MAAM;gBACN,WAAW,QAAQ,SAAA;gBACnB,SAAS;oBAAC;wBAAE,MAAM;wBAAiB,MAAM,QAAQ,SAAA;oBAAU,CAAC;iBAAA;YAC9D;SACF,GACA,CAAC,CAAA;WACD,QAAQ,OAAA,GACR;YAAC;gBAAE,MAAM;gBAAiB,MAAM,QAAQ,OAAA;YAAQ,CAAC;SAAA,GACjD,CAAC,CAAA;KACP;AAEJ;;ACzCO,SAAS,iBAAiB,QAAA,EAAkC;IACjE,OAAO,SAAS,GAAA,CAAI,CAAA,UAAA,CAAY;YAC9B,GAAG,OAAA;YACH,OAAO,gBAAgB,OAAO;QAChC,CAAA,CAAE;AACJ;;ACDO,SAAS,gBAAgB,IAAA,EAAW,IAAA,EAAoB;IAE7D,IAAI,SAAS,MAAM,OAAO;IAG1B,IAAI,QAAQ,QAAQ,QAAQ,MAAM,OAAO;IAGzC,IAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAC9C,OAAO,SAAS;IAGlB,IAAI,KAAK,WAAA,KAAgB,KAAK,WAAA,EAAa,OAAO;IAGlD,IAAI,gBAAgB,QAAQ,gBAAgB,MAAM;QAChD,OAAO,KAAK,OAAA,CAAQ,MAAM,KAAK,OAAA,CAAQ;IACzC;IAGA,IAAI,MAAM,OAAA,CAAQ,IAAI,GAAG;QACvB,IAAI,KAAK,MAAA,KAAW,KAAK,MAAA,EAAQ,OAAO;QACxC,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,MAAA,EAAQ,IAAK;YACpC,IAAI,CAAC,gBAAgB,IAAA,CAAK,CAAC,CAAA,EAAG,IAAA,CAAK,CAAC,CAAC,GAAG,OAAO;QACjD;QACA,OAAO;IACT;IAGA,MAAM,QAAQ,OAAO,IAAA,CAAK,IAAI;IAC9B,MAAM,QAAQ,OAAO,IAAA,CAAK,IAAI;IAC9B,IAAI,MAAM,MAAA,KAAW,MAAM,MAAA,EAAQ,OAAO;IAG1C,KAAA,MAAW,OAAO,MAAO;QACvB,IAAI,CAAC,MAAM,QAAA,CAAS,GAAG,GAAG,OAAO;QACjC,IAAI,CAAC,gBAAgB,IAAA,CAAK,GAAG,CAAA,EAAG,IAAA,CAAK,GAAG,CAAC,GAAG,OAAO;IACrD;IAEA,OAAO;AACT;;AC7CA,eAAsB,6BACpB,sBAAA,EACA;IACA,IAAI,CAAC,wBAAwB;QAC3B,OAAO,CAAC,CAAA;IACV;IAKA,IACE,WAAW,QAAA,IACX,kCAAkC,WAAW,QAAA,EAC7C;QACA,OAAO,QAAQ,GAAA,CACb,MAAM,IAAA,CAAK,sBAAsB,EAAE,GAAA,CAAI,OAAM,eAAc;YACzD,MAAM,EAAE,IAAA,EAAM,IAAA,CAAK,CAAA,GAAI;YAEvB,MAAM,UAAU,MAAM,IAAI,QAAgB,CAAC,SAAS,WAAW;gBAC7D,MAAM,SAAS,IAAI,WAAW;gBAC9B,OAAO,MAAA,GAAS,CAAA,gBAAe;oBAtBzC,IAAA;oBAuBY,QAAA,CAAQ,KAAA,YAAY,MAAA,KAAZ,OAAA,KAAA,IAAA,GAAoB,MAAgB;gBAC9C;gBACA,OAAO,OAAA,GAAU,CAAA,QAAS,OAAO,KAAK;gBACtC,OAAO,aAAA,CAAc,UAAU;YACjC,CAAC;YAED,OAAO;gBACL;gBACA,aAAa;gBACb,KAAK;YACP;QACF,CAAC;IAEL;IAEA,IAAI,MAAM,OAAA,CAAQ,sBAAsB,GAAG;QACzC,OAAO;IACT;IAEA,MAAM,IAAI,MAAM,0BAA0B;AAC5C;;ACtCA,IAAMC,WAAU,KAAK,UAAA,CAAW,CAAC;AAGjC,SAASC,cAAa,MAAA,EAAsB,WAAA,EAAqB;IAC/D,MAAM,qBAAqB,IAAI,WAAW,WAAW;IAErD,IAAI,SAAS;IACb,KAAA,MAAW,SAAS,OAAQ;QAC1B,mBAAmB,GAAA,CAAI,OAAO,MAAM;QACpC,UAAU,MAAM,MAAA;IAClB;IACA,OAAO,MAAA,GAAS;IAEhB,OAAO;AACT;AAEA,eAAsB,uBAAuB,EAC3C,MAAA,EACA,UAAA,EACA,WAAA,EACA,sBAAA,EACA,0BAAA,EACA,iBAAA,EACF,EAqBkB;IAIhB,MAAM,SAAS,OAAO,SAAA,CAAU;IAChC,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,SAAuB,CAAC,CAAA;IAC9B,IAAI,cAAc;IAElB,MAAO,KAAM;QACX,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,IAAA,CAAK;QAEpC,IAAI,OAAO;YACT,OAAO,IAAA,CAAK,KAAK;YACjB,eAAe,MAAM,MAAA;YACrB,IAAI,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,KAAMD,UAAS;gBAEvC;YACF;QACF;QAEA,IAAI,OAAO,MAAA,KAAW,GAAG;YACvB;QACF;QAEA,MAAM,qBAAqBC,cAAa,QAAQ,WAAW;QAC3D,cAAc;QAEd,MAAM,cAAc,QACjB,MAAA,CAAO,oBAAoB;YAAE,QAAQ;QAAK,CAAC,EAC3C,KAAA,CAAM,IAAI,EACV,MAAA,CAAO,CAAA,OAAQ,SAAS,EAAE,EAC1B,GAAA,CAAI,wBAAwB;QAE/B,KAAA,MAAW,EAAE,IAAA,EAAM,OAAAC,MAAAA,CAAM,CAAA,IAAK,YAAa;YACzC,OAAQ,MAAM;gBACZ,KAAK;oBACH,MAAA,CAAM,cAAA,OAAA,KAAA,IAAA,WAAaA,OAAAA;oBACnB;gBACF,KAAK;oBACH,MAAA,CAAM,eAAA,OAAA,KAAA,IAAA,YAAcA,OAAAA;oBACpB;gBACF,KAAK;oBACH,MAAA,CAAM,0BAAA,OAAA,KAAA,IAAA,uBAAyBA,OAAAA;oBAC/B;gBACF,KAAK;oBACH,MAAA,CAAM,8BAAA,OAAA,KAAA,IAAA,2BAA6BA,OAAAA;oBACnC;gBACF,KAAK;oBACH,MAAA,CAAM,qBAAA,OAAA,KAAA,IAAA,kBAAoBA,OAAAA;oBAC1B;gBACF;oBAAS;wBACP,MAAM,kBAAyB;wBAC/B,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,eAAe,EAAE;oBAChE;YACF;QACF;IACF;AACF;;;AEtGO,SAAS,UACdC,UAAAA,EACA,OAAA,EASgB;IAhBlB,IAAA;IAkBE,MAAM,gBAAA,CAAgB,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,aAAA,KAAT,OAAA,KAA0B;IAEhD,OAAO,WACL,4MAAA,EAAgBA,YAAW;QACzB,cAAc,gBAAgB,SAAS;QACvC,QAAQ;IACV,CAAC,GACD;QACE,UAAU,CAAA,UAAS;YACjB,MAAM,SAASA,WAAU,SAAA,CAAU,KAAK;YACxC,OAAO,OAAO,OAAA,GACV;gBAAE,SAAS;gBAAM,OAAO,OAAO,IAAA;YAAK,IACpC;gBAAE,SAAS;gBAAO,OAAO,OAAO,KAAA;YAAM;QAC5C;IACF;AAEJ;;AD1BA,IAAM,eAAe,OAAO,GAAA,CAAI,kBAAkB;AAyB3C,SAAS,WACdC,WAAAA,EACA,EACE,QAAA,EACF,GAII,CAAC,CAAA,EACW;IAChB,OAAO;QACL,CAAC,YAAY,CAAA,EAAG;QAChB,OAAO,KAAA;QAAA,gCAAA;QACP,8KAAC,kBAAe,CAAA,EAAG;QACnB,YAAAA;QACA;IACF;AACF;AAEA,SAAS,SAAS,KAAA,EAAiC;IACjD,OACE,OAAO,UAAU,YACjB,UAAU,QACV,gBAAgB,SAChB,KAAA,CAAM,YAAY,CAAA,KAAM,QACxB,gBAAgB,SAChB,cAAc;AAElB;AAEO,SAAS,SACd,MAAA,EACgB;IAChB,OAAO,SAAS,MAAM,IAAI,SAAS,UAAU,MAAM;AACrD;;AEhEO,SAAS,uBAAuB,EACrC,6BAAA,EACA,oBAAA,EACA,QAAA,EACA,QAAA,EACF,EAKG;IAbH,IAAA;IAcE,MAAM,cAAc,QAAA,CAAS,SAAS,MAAA,GAAS,CAAC,CAAA;IAChD,OAAA,mCAAA;IAEE,WAAW,KAAA,kCAAA;IAEX,eAAe,QAAA,mFAAA;IAAA,CAEd,SAAS,MAAA,GAAS,wBACjB,6BAA6B,YAAY,eAAe,MACtD,6BAAA,KAAA,oCAAA;IAEJ,yCAAyC,WAAW,KAAA,uCAAA;IAAA,CAAA,CAEnD,KAAA,6BAA6B,YAAY,eAAe,CAAA,KAAxD,OAAA,KAA6D,CAAA,IAAK;AAEvE;AAOO,SAAS,yCACd,OAAA,EAGA;IACA,IAAI,QAAQ,IAAA,KAAS,aAAa;QAChC,OAAO;IACT;IAEA,MAAM,qBAAqB,QAAQ,KAAA,CAAM,MAAA,CAAO,CAAC,WAAW,MAAM,UAAU;QAC1E,OAAO,KAAK,IAAA,KAAS,eAAe,QAAQ;IAC9C,GAAG,CAAA,CAAE;IAEL,MAAM,0BAA0B,QAAQ,KAAA,CACrC,KAAA,CAAM,qBAAqB,CAAC,EAC5B,MAAA,CAAO,CAAA,OAAQ,KAAK,IAAA,KAAS,iBAAiB;IAEjD,OACE,wBAAwB,MAAA,GAAS,KACjC,wBAAwB,KAAA,CAAM,CAAA,OAAQ,YAAY,KAAK,cAAc;AAEzE;;AC9CO,SAAS,qBAAqB,EACnC,QAAA,EACA,UAAA,EACA,YAAY,MAAA,EACd,EAIG;IAnBH,IAAA;IAoBE,MAAM,cAAc,QAAA,CAAS,SAAS,MAAA,GAAS,CAAC,CAAA;IAEhD,MAAM,iBAAiB,YAAY,KAAA,CAAM,IAAA,CACvC,CAAC,OACC,KAAK,IAAA,KAAS,qBACd,KAAK,cAAA,CAAe,UAAA,KAAe;IAGvC,IAAI,kBAAkB,MAAM;QAC1B;IACF;IAEA,MAAM,aAAa;QACjB,GAAG,eAAe,cAAA;QAClB,OAAO;QACP;IACF;IAEA,eAAe,cAAA,GAAiB;IAEhC,YAAY,eAAA,GAAA,CAAkB,KAAA,YAAY,eAAA,KAAZ,OAAA,KAAA,IAAA,GAA6B,GAAA,CACzD,CAAA,iBACE,eAAe,UAAA,KAAe,aAAa,aAAa;AAE9D", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}}, {"offset": {"line": 4305, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@assistant-ui/react-ai-sdk/src/frontendTools.ts"], "sourcesContent": ["import { jsonSchema } from \"@ai-sdk/ui-utils\";\nimport type { JSONSchema7 } from \"json-schema\";\n\nexport const frontendTools = (\n  tools: Record<string, { description?: string; parameters: JSONSchema7 }>,\n) =>\n  Object.fromEntries(\n    Object.entries(tools).map(([name, tool]) => [\n      name,\n      {\n        ...(tool.description ? { description: tool.description } : undefined),\n        parameters: jsonSchema(tool.parameters),\n      },\n    ]),\n  );\n"], "names": [], "mappings": ";;;;AAAA,SAAS,kBAAkB;;AAGpB,IAAM,gBAAgB,CAC3B,QAEA,OAAO,WAAA,CACL,OAAO,OAAA,CAAQ,KAAK,EAAE,GAAA,CAAI,CAAC,CAAC,MAAM,IAAI,CAAA,GAAM;YAC1C;YACA;gBACE,GAAI,KAAK,WAAA,GAAc;oBAAE,aAAa,KAAK,WAAA;gBAAY,IAAI,KAAA,CAAA;gBAC3D,uMAAY,aAAA,EAAW,KAAK,UAAU;YACxC;SACD", "ignoreList": [0]}}]}