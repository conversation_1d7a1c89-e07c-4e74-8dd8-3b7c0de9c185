{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/api/chat/route.ts"], "sourcesContent": ["import { anthropic } from \"@ai-sdk/anthropic\";\nimport { frontendTools } from \"@assistant-ui/react-ai-sdk\";\nimport { streamText, experimental_createMCPClient } from \"ai\";\n\nexport const runtime = \"edge\";\nexport const maxDuration = 30;\n\nexport async function POST(req: Request) {\n  const { messages, system, tools } = await req.json();\n\n  // Check if API key is configured\n  if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {\n    return new Response(\n      JSON.stringify({\n        error: 'Anthropic API key not configured. Please set ANTHROPIC_API_KEY in your .env.local file.'\n      }),\n      {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      }\n    );\n  }\n\n  let mcpClient;\n  let mcpTools = {};\n\n  try {\n    // Connect to the n8n MCP server via SSE\n    mcpClient = await experimental_createMCPClient({\n      transport: {\n        type: 'sse',\n        url: 'https://n8n.datosoperations.com/mcp/b2a01660-20a1-4edd-8b31-f9cdaa1810e5/sse',\n      },\n    });\n\n    // Get tools from the MCP server\n    mcpTools = await mcpClient.tools();\n    console.log('MCP Tools available:', Object.keys(mcpTools));\n  } catch (error) {\n    console.error('Failed to connect to MCP server:', error);\n    // Continue without MCP tools if connection fails\n  }\n\n  const result = streamText({\n    model: anthropic(\"claude-3-5-sonnet-20241022\"),\n    messages,\n    // forward system prompt and tools from the frontend\n    toolCallStreaming: true,\n    system,\n    tools: {\n      ...frontendTools(tools),\n      ...mcpTools, // Add MCP tools from n8n server\n    },\n    onError: console.log,\n  });\n\n  // Clean up MCP client after streaming is complete\n  result.finishPromise.finally(() => {\n    if (mcpClient) {\n      mcpClient.close().catch(console.error);\n    }\n  });\n\n  return result.toDataStreamResponse();\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,UAAU;AAChB,MAAM,cAAc;AAEpB,eAAe,KAAK,GAAY;IACrC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,IAAI;IAElD,iCAAiC;IACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK,+BAA+B;QACrG,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;YACb,OAAO;QACT,IACA;YACE,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IAEJ;IAEA,IAAI;IACJ,IAAI,WAAW,CAAC;IAEhB,IAAI;QACF,wCAAwC;QACxC,YAAY,MAAM,CAAA,GAAA,8JAAA,CAAA,+BAA4B,AAAD,EAAE;YAC7C,WAAW;gBACT,MAAM;gBACN,KAAK;YACP;QACF;QAEA,gCAAgC;QAChC,WAAW,MAAM,UAAU,KAAK;QAChC,QAAQ,GAAG,CAAC,wBAAwB,OAAO,IAAI,CAAC;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IAClD,iDAAiD;IACnD;IAEA,MAAM,SAAS,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE;QACxB,OAAO,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE;QACjB;QACA,oDAAoD;QACpD,mBAAmB;QACnB;QACA,OAAO;YACL,GAAG,CAAA,GAAA,0LAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;YACvB,GAAG,QAAQ;QACb;QACA,SAAS,QAAQ,GAAG;IACtB;IAEA,kDAAkD;IAClD,OAAO,aAAa,CAAC,OAAO,CAAC;QAC3B,IAAI,WAAW;YACb,UAAU,KAAK,GAAG,KAAK,CAAC,QAAQ,KAAK;QACvC;IACF;IAEA,OAAO,OAAO,oBAAoB;AACpC"}}]}