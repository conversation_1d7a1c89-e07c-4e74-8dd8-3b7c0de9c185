{"name": "secure-json-parse", "version": "2.7.0", "description": "JSON parse with prototype poisoning protection", "main": "index.js", "types": "types/index.d.ts", "scripts": {"benchmark": "cd benchmarks && npm install && npm run all", "lint": "standard", "test": "nyc npm run test:unit && npm run test:typescript", "test:unit": "tape \"test/*.test.js\"", "test:typescript": "tsd", "test:browser": "airtap test/*.test.js"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/secure-json-parse.git"}, "keywords": ["JSON", "parse", "safe", "security", "prototype", "pollution"], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/fastify/secure-json-parse/issues"}, "homepage": "https://github.com/fastify/secure-json-parse#readme", "devDependencies": {"airtap": "^4.0.1", "airtap-playwright": "^1.0.1", "nyc": "^14.1.1", "playwright": "^1.7.1", "standard": "^17.0.0", "tape": "^5.1.1", "tsd": "^0.25.0"}}